/**
 * Magic 游戏辅助脚本 - 通用侧滑抽屉逻辑
 * 基于AutoXjs稳定API开发
 * 支持在所有页面中使用的通用抽屉系统
 */

// 导入全局样式
var 全局样式 = require('../全局样式/公共样式.js');

// 导入配置管理
var 配置管理 = require('../../存储数据/配置管理.js');

// 抽屉状态管理
var 抽屉状态 = {
    已打开: false,
    当前页面: null,
    权限状态: {
        无障碍服务: false,
        悬浮窗权限: false,
        截图权限: false,
        前台服务权限: false,    // 新增：前台服务权限
        读取权限: false,        // 新增：存储读取权限
        通知访问权限: false,    // 新增：通知访问权限
        控制台显示: false       // 新增：控制台显示权限
    }
};

/**
 * 初始化抽屉逻辑 - 通用版本，支持所有页面
 */
function 初始化抽屉逻辑(当前页面名称) {
    try {
        console.log("初始化通用抽屉逻辑...", 当前页面名称);

        // 记录当前页面
        抽屉状态.当前页面 = 当前页面名称 || "未知页面";



        // 绑定菜单按钮点击事件
        绑定菜单按钮事件();

        // 绑定遮罩点击事件
        绑定遮罩点击事件();

        // 绑定权限开关事件
        绑定权限开关事件();

        // 绑定导航项点击事件
        绑定导航项事件();

        // 恢复权限状态
        恢复权限状态();

        // 初始化权限状态显示
        更新权限状态显示();

        console.log("通用抽屉逻辑初始化完成");
    } catch (e) {
        console.error("初始化抽屉逻辑失败:", e);
    }
}



/**
 * 绑定菜单按钮事件
 */
function 绑定菜单按钮事件() {
    if (ui.菜单按钮) {
        ui.菜单按钮.on("click", function() {
            try {
                console.log("菜单按钮被点击，当前页面:", 抽屉状态.当前页面);
                if (抽屉状态.已打开) {
                    关闭抽屉();
                } else {
                    打开抽屉();
                }
            } catch (e) {
                console.error("菜单按钮点击处理失败:", e);
            }
        });
        console.log("菜单按钮事件绑定成功");
    } else {
        console.warn("菜单按钮控件未找到，页面:", 抽屉状态.当前页面);
    }
}

/**
 * 绑定遮罩点击事件
 */
function 绑定遮罩点击事件() {
    if (ui.抽屉遮罩) {
        ui.抽屉遮罩.on("click", function() {
            try {
                关闭抽屉();
            } catch (e) {
                console.error("遮罩点击处理失败:", e);
            }
        });
        console.log("遮罩点击事件绑定成功");
    } else {
        console.warn("抽屉遮罩控件未找到");
    }
}

/**
 * 打开抽屉
 */
function 打开抽屉() {
    try {
        // 显示遮罩层并设置半透明背景
        if (ui.抽屉遮罩) {
            ui.抽屉遮罩.attr("visibility", "visible");
            ui.抽屉遮罩.attr("bg", "#80000000"); // 半透明黑色
        }

        // 显示抽屉面板
        if (ui.抽屉面板) {
            ui.抽屉面板.attr("visibility", "visible");
        }

        抽屉状态.已打开 = true;
        toast("抽屉已打开");
        console.log("抽屉已打开");
    } catch (e) {
        console.error("打开抽屉失败:", e);
        toast("打开抽屉失败");
    }
}

/**
 * 关闭抽屉
 */
function 关闭抽屉() {
    try {
        // 隐藏抽屉面板
        if (ui.抽屉面板) {
            ui.抽屉面板.attr("visibility", "gone");
        }

        // 隐藏遮罩层并恢复透明背景
        if (ui.抽屉遮罩) {
            ui.抽屉遮罩.attr("visibility", "gone");
            ui.抽屉遮罩.attr("bg", "#00000000"); // 完全透明
        }

        抽屉状态.已打开 = false;
        toast("抽屉已关闭");
        console.log("抽屉已关闭");
    } catch (e) {
        console.error("关闭抽屉失败:", e);
        toast("关闭抽屉失败");
    }
}

/**
 * 绑定权限开关事件
 */
function 绑定权限开关事件() {
    // 读取权限开关
    if (ui.读取权限开关) {
        ui.读取权限开关.on("check", function(checked) {
            try {
                if (checked) {
                    // 用户想开启存储权限
                    var 当前权限状态 = 检查存储权限();

                    if (当前权限状态) {
                        // 已有权限，直接开启并验证功能
                        抽屉状态.权限状态.读取权限 = true;

                        // 保存权限状态到配置文件
                        配置管理.保存权限状态("读取权限", true);

                        // 验证存储权限功能是否真正可用
                        验证存储权限功能();
                    } else {
                        // 没有权限，请求权限
                        toast("正在请求存储权限...");
                        请求存储权限();
                        // 暂时回滚开关状态，等待权限授权结果
                        ui.读取权限开关.checked = false;
                        抽屉状态.权限状态.读取权限 = false;
                    }
                } else {
                    // 用户想关闭存储权限
                    抽屉状态.权限状态.读取权限 = false;

                    // 保存权限状态到配置文件
                    配置管理.保存权限状态("读取权限", false);

                    toast("存储权限已关闭");
                    console.log("存储权限已关闭");

                    // 提示用户如何彻底关闭权限
                    setTimeout(function() {
                        dialogs.alert("存储权限关闭",
                            "应用内存储权限已关闭。\n\n" +
                            "如需彻底撤销系统权限，请到：\n" +
                            "设置 → 应用管理 → Magic游戏助手 → 权限管理 → 存储权限\n\n" +
                            "手动关闭存储权限。"
                        );
                    }, 500);
                }
            } catch (e) {
                console.error("读取权限开关处理失败:", e);
                // 开关状态回滚
                ui.读取权限开关.checked = !checked;
                抽屉状态.权限状态.读取权限 = !checked;
            }
        });
        console.log("读取权限开关事件绑定成功");
    }

    // 悬浮窗权限开关
    if (ui.悬浮窗权限开关) {
        ui.悬浮窗权限开关.on("check", function(checked) {
            try {
                抽屉状态.权限状态.悬浮窗权限 = checked;

                // 保存权限状态到配置文件
                配置管理.保存权限状态("悬浮窗权限", checked);

                var 状态文本 = checked ? "已开启" : "已关闭";
                toast("悬浮球" + 状态文本);
                console.log("悬浮球权限:", 状态文本);

                // 集成悬浮球逻辑
                if (checked) {
                    // 开启悬浮球
                    启动悬浮球();
                } else {
                    // 关闭悬浮球
                    关闭悬浮球();
                }
            } catch (e) {
                console.error("悬浮球开关处理失败:", e);
                // 开关状态回滚
                ui.悬浮窗权限开关.checked = !checked;
            }
        });
        console.log("悬浮窗权限开关事件绑定成功");
    }

    // 前台服务权限开关
    if (ui.前台服务权限开关) {
        ui.前台服务权限开关.on("check", function(checked) {
            try {
                抽屉状态.权限状态.前台服务权限 = checked;

                // 保存权限状态到配置文件
                配置管理.保存权限状态("前台服务权限", checked);

                var 状态文本 = checked ? "已开启" : "已关闭";
                toast("前台服务" + 状态文本);
                console.log("前台服务权限:", 状态文本);

                // 集成前台服务逻辑
                if (checked) {
                    // 开启前台服务
                    启动前台服务();
                } else {
                    // 关闭前台服务
                    停止前台服务();
                }
            } catch (e) {
                console.error("前台服务开关处理失败:", e);
                // 开关状态回滚
                ui.前台服务权限开关.checked = !checked;
            }
        });
        console.log("前台服务权限开关事件绑定成功");
    }

    // 通知访问权限开关 - 重构版本
    console.log("检查通知访问权限开关控件:", ui.通知访问权限开关 ? "找到" : "未找到");
    if (ui.通知访问权限开关) {
        console.log("开始绑定通知访问权限开关事件...");
        ui.通知访问权限开关.on("check", function(checked) {
            console.log("通知访问权限开关被点击，状态:", checked ? "开启" : "关闭");
            try {
                if (checked) {
                    // 用户想开启通知访问权限
                    console.log("用户要求开启通知访问权限...");

                    // 检查当前权限状态
                    var 当前权限状态 = 检查通知访问权限();

                    if (当前权限状态) {
                        // 已有权限，直接启用功能
                        var 启用成功 = 启用通知访问功能();
                        if (启用成功) {
                            抽屉状态.权限状态.通知访问权限 = true;
                            配置管理.保存权限状态("通知访问权限", true);
                            console.log("✅ 通知访问权限已开启");
                        } else {
                            // 启用失败，回滚开关状态
                            ui.通知访问权限开关.checked = false;
                            抽屉状态.权限状态.通知访问权限 = false;
                            配置管理.保存权限状态("通知访问权限", false);
                        }
                    } else {
                        // 没有权限，请求权限
                        console.log("检测到无通知访问权限，开始申请...");
                        请求通知访问权限();

                        // 暂时回滚开关状态，等待用户手动设置
                        ui.通知访问权限开关.checked = false;
                        抽屉状态.权限状态.通知访问权限 = false;

                        // 启动权限状态监听器
                        启动通知访问权限监听器();
                    }
                } else {
                    // 用户想关闭通知访问权限
                    console.log("用户要求关闭通知访问权限...");

                    // 保存权限状态到配置文件
                    抽屉状态.权限状态.通知访问权限 = false;
                    配置管理.保存权限状态("通知访问权限", false);

                    // Android系统限制：无法编程方式关闭通知访问权限
                    // 只能跳转到设置页面让用户手动关闭
                    关闭通知访问权限();
                }
            } catch (e) {
                console.error("通知访问权限开关处理失败:", e);
                toast("操作失败: " + e.message);

                // 开关状态回滚
                ui.通知访问权限开关.checked = !checked;
                抽屉状态.权限状态.通知访问权限 = !checked;
            }
        });
        console.log("通知访问权限开关事件绑定成功");
    }

    // 截图权限开关（优化版本，避免卡死）
    console.log("检查截图权限开关控件:", ui.截图权限开关 ? "找到" : "未找到");
    if (ui.截图权限开关) {
        console.log("开始绑定截图权限开关事件...");
        ui.截图权限开关.on("check", function(checked) {
            console.log("截图权限开关被点击，状态:", checked ? "开启" : "关闭");
            try {
                if (checked) {
                    // 用户想开启截图权限
                    console.log("用户要求开启截图权限...");
                    var 当前权限状态 = 检查截图权限();

                    if (当前权限状态) {
                        // 已有权限，直接开启
                        抽屉状态.权限状态.截图权限 = true;
                        配置管理.保存权限状态("截图权限", true);
                        toast("截图权限已开启");
                        console.log("截图权限已开启");
                    } else {
                        // 没有权限，异步请求权限（避免卡死）
                        console.log("开始异步申请截图权限...");
                        toast("正在申请截图权限，请在弹出的对话框中授权");

                        // 暂时回滚开关状态，等待权限授权结果
                        ui.截图权限开关.checked = false;
                        抽屉状态.权限状态.截图权限 = false;

                        // 异步申请权限
                        请求截图权限();
                    }
                } else {
                    // 用户想关闭截图权限
                    console.log("用户要求关闭截图权限...");
                    抽屉状态.权限状态.截图权限 = false;
                    配置管理.保存权限状态("截图权限", false);
                    toast("截图权限已关闭");
                    console.log("截图权限已关闭");
                }
            } catch (e) {
                console.error("截图权限开关处理失败:", e);
                // 开关状态回滚
                ui.截图权限开关.checked = !checked;
                抽屉状态.权限状态.截图权限 = !checked;
                toast("截图权限操作失败: " + e.message);
            }
        });
        console.log("截图权限开关事件绑定成功");
    }

    // 无障碍服务开关（基于AutoXjs官方文档重新实现）
    console.log("检查无障碍权限开关控件:", ui.无障碍权限开关 ? "找到" : "未找到");
    if (ui.无障碍权限开关) {
        console.log("开始绑定无障碍权限开关事件...");
        ui.无障碍权限开关.on("check", function(checked) {
            console.log("无障碍权限开关被点击，状态:", checked ? "开启" : "关闭");
            try {
                if (checked) {
                    // 用户想开启无障碍服务
                    var 当前权限状态 = 检查无障碍服务权限();

                    if (当前权限状态) {
                        // 已有权限，启用无障碍服务功能
                        var 功能启用成功 = 启用无障碍服务功能();
                        if (功能启用成功) {
                            抽屉状态.权限状态.无障碍服务 = true;
                            配置管理.保存权限状态("无障碍服务", true);
                            toast("无障碍服务已开启");
                            console.log("无障碍服务已开启");
                        } else {
                            // 功能启用失败，回滚开关
                            ui.无障碍权限开关.checked = false;
                            抽屉状态.权限状态.无障碍服务 = false;
                            配置管理.保存权限状态("无障碍服务", false);
                        }
                    } else {
                        // 没有权限，跳转到系统设置页面
                        请求无障碍服务权限();
                        // 暂时回滚开关状态，等待用户手动设置
                        ui.无障碍权限开关.checked = false;
                        抽屉状态.权限状态.无障碍服务 = false;

                        // 延迟检查权限状态（用户可能从设置页面返回）
                        setTimeout(function() {
                            var 权限状态 = 检查无障碍服务权限();
                            if (权限状态) {
                                // 权限已开启，启用功能并同步开关状态
                                var 功能启用成功 = 启用无障碍服务功能();
                                if (功能启用成功 && ui.无障碍权限开关) {
                                    ui.无障碍权限开关.checked = true;
                                    抽屉状态.权限状态.无障碍服务 = true;
                                    toast("无障碍服务已开启");
                                    console.log("无障碍服务已开启");
                                }
                            } else {
                                // 权限仍未开启，保持开关关闭状态
                                if (ui.无障碍权限开关) {
                                    ui.无障碍权限开关.checked = false;
                                }
                                抽屉状态.权限状态.无障碍服务 = false;
                            }
                        }, 5000); // 给用户足够时间在设置页面操作
                    }
                } else {
                    // 用户想关闭无障碍服务
                    console.log("用户要求关闭无障碍服务...");
                    var 功能停用成功 = 停用无障碍服务功能();
                    if (功能停用成功) {
                        抽屉状态.权限状态.无障碍服务 = false;
                        配置管理.保存权限状态("无障碍服务", false);
                        console.log("无障碍服务已关闭");

                        // 延迟检查服务是否真正关闭
                        setTimeout(function() {
                            var 服务状态 = 检查无障碍服务权限();
                            if (!服务状态) {
                                console.log("✅ 确认无障碍服务已完全关闭");
                                toast("无障碍服务已完全关闭");
                            } else {
                                console.warn("⚠️ 无障碍服务可能未完全关闭");
                                toast("无障碍服务关闭可能不完整");
                            }
                        }, 1000);
                    } else {
                        // 功能停用失败，回滚开关
                        ui.无障碍权限开关.checked = true;
                        抽屉状态.权限状态.无障碍服务 = true;
                        console.error("无障碍服务关闭失败，开关状态已回滚");
                    }
                }
            } catch (e) {
                console.error("无障碍服务开关处理失败:", e);
                // 开关状态回滚
                ui.无障碍权限开关.checked = !checked;
                抽屉状态.权限状态.无障碍服务 = !checked;
            }
        });
        console.log("无障碍服务开关事件绑定成功");
    }

    // 控制台显示开关
    console.log("检查控制台显示开关控件:", ui.控制台显示开关 ? "找到" : "未找到");
    if (ui.控制台显示开关) {
        console.log("开始绑定控制台显示开关事件...");
        ui.控制台显示开关.on("check", function(checked) {
            console.log("控制台显示开关被点击，状态:", checked ? "开启" : "关闭");
            try {
                if (checked) {
                    // 用户想显示控制台
                    console.log("用户要求显示控制台...");
                    toast("正在显示控制台，请稍候...");

                    // 异步显示控制台
                    var 显示成功 = 显示控制台();
                    if (显示成功) {
                        // 异步操作已启动，更新状态
                        抽屉状态.权限状态.控制台显示 = true;
                        配置管理.保存权限状态("控制台显示", true);
                        console.log("控制台显示操作已启动");

                        // 延迟验证控制台是否真正显示
                        setTimeout(function() {
                            try {
                                // 这里可以添加验证逻辑
                                console.log("✅ 控制台显示操作完成");
                            } catch (e) {
                                console.warn("控制台显示验证失败:", e);
                            }
                        }, 1000);
                    } else {
                        // 显示失败，回滚开关状态
                        ui.控制台显示开关.checked = false;
                        抽屉状态.权限状态.控制台显示 = false;
                        toast("控制台显示失败");
                    }
                } else {
                    // 用户想隐藏控制台
                    console.log("用户要求隐藏控制台...");
                    toast("正在隐藏控制台，请稍候...");

                    // 异步隐藏控制台
                    var 隐藏成功 = 隐藏控制台();
                    if (隐藏成功) {
                        // 异步操作已启动，更新状态
                        抽屉状态.权限状态.控制台显示 = false;
                        配置管理.保存权限状态("控制台显示", false);
                        console.log("控制台隐藏操作已启动");

                        // 延迟验证控制台是否真正隐藏
                        setTimeout(function() {
                            try {
                                // 这里可以添加验证逻辑
                                console.log("✅ 控制台隐藏操作完成");
                            } catch (e) {
                                console.warn("控制台隐藏验证失败:", e);
                            }
                        }, 1000);
                    } else {
                        // 隐藏失败，回滚开关状态
                        ui.控制台显示开关.checked = true;
                        抽屉状态.权限状态.控制台显示 = true;
                        toast("控制台隐藏失败");
                    }
                }
            } catch (e) {
                console.error("控制台显示开关处理失败:", e);
                // 开关状态回滚
                ui.控制台显示开关.checked = !checked;
                抽屉状态.权限状态.控制台显示 = !checked;
                toast("控制台操作失败: " + e.message);
            }
        });
        console.log("控制台显示开关事件绑定成功");
    }

    // 截图权限开关 - 重复绑定已删除，避免冲突

    console.log("权限开关事件绑定完成");
}

/**
 * 绑定导航项事件
 */
function 绑定导航项事件() {
    // 关于应用
    if (ui.关于应用项) {
        ui.关于应用项.on("click", function() {
            try {
                显示关于应用对话框();
            } catch (e) {
                console.error("关于应用点击处理失败:", e);
            }
        });
    }

    console.log("导航项事件绑定完成");
}

/**
 * 更新权限状态显示（Android 9.0优化版本）
 */
function 更新权限状态显示() {
    try {
        console.log("开始更新权限状态显示（Android 9.0优化）...");

        // 检查无障碍服务权限状态（仅检查，不自动同步开关）
        检查无障碍服务权限状态(true);

        // 检查并同步悬浮球权限状态
        console.log("2. 检查悬浮球权限状态...");
        检查悬浮球权限状态();

        // 检查并同步前台服务权限状态
        console.log("3. 检查前台服务权限状态...");
        检查前台服务状态();

        // 初始化存储权限状态（不自动同步开关）
        console.log("4. 初始化存储权限状态...");
        初始化存储权限状态();

        // 检查并同步通知访问权限状态 - 重构版本
        console.log("5. 检查通知访问权限状态...");
        检查通知访问权限状态并同步开关();

        // 检查并同步截图权限状态（仅检查，不自动同步开关）
        console.log("6. 检查截图权限状态...");
        检查截图权限状态(true);

        // 检查控制台显示状态
        console.log("7. 检查控制台显示状态...");
        检查控制台显示状态();

        console.log("权限状态显示更新完成（Android 9.0优化）");
    } catch (e) {
        console.error("更新权限状态显示失败:", e);
    }
}

/**
 * 显示关于应用对话框
 */
function 显示关于应用对话框() {
    try {
        dialogs.alert("关于应用",
            "Magic 游戏助手 v1.0.0\n\n" +
            "基于 AutoXjs ozobiozobi v6.5.8.17 开发\n\n" +
            "一款专业的游戏辅助脚本应用\n" +
            "提供高效、稳定的游戏辅助功能\n\n" +
            "© 2025 Magic Team"
        );
    } catch (e) {
        console.error("显示关于应用对话框失败:", e);
    }
}

/**
 * 获取抽屉状态
 */
function 获取抽屉状态() {
    return 抽屉状态;
}

/**
 * 获取权限状态
 */
function 获取权限状态() {
    return 抽屉状态.权限状态;
}

/**
 * 恢复权限状态
 * 从配置文件中恢复所有权限开关的状态
 */
function 恢复权限状态() {
    try {
        console.log("开始恢复权限状态...");

        // 从配置文件获取所有权限状态
        var 保存的权限状态 = 配置管理.获取所有权限状态();
        console.log("从配置文件读取的权限状态:", JSON.stringify(保存的权限状态, null, 2));

        // 恢复内存中的权限状态
        Object.keys(保存的权限状态).forEach(function(权限名称) {
            var 状态 = 保存的权限状态[权限名称];
            抽屉状态.权限状态[权限名称] = 状态;
            console.log("恢复权限状态:", 权限名称, "=", 状态);
        });

        // 恢复UI开关状态
        恢复UI开关状态(保存的权限状态);

        // 恢复功能状态（重要：自动启动之前开启的功能）
        恢复功能状态(保存的权限状态);

        console.log("权限状态恢复完成");

    } catch (e) {
        console.error("恢复权限状态失败:", e);
    }
}

/**
 * 恢复UI开关状态
 * @param {object} 权限状态 - 权限状态对象
 */
function 恢复UI开关状态(权限状态) {
    try {
        console.log("开始恢复UI开关状态...");

        // 恢复各个开关的UI状态
        if (ui.无障碍权限开关 && 权限状态.无障碍服务 !== undefined) {
            ui.无障碍权限开关.checked = 权限状态.无障碍服务;
            console.log("恢复无障碍权限开关:", 权限状态.无障碍服务);
        }

        if (ui.悬浮窗权限开关 && 权限状态.悬浮窗权限 !== undefined) {
            ui.悬浮窗权限开关.checked = 权限状态.悬浮窗权限;
            console.log("恢复悬浮窗权限开关:", 权限状态.悬浮窗权限);
        }

        if (ui.截图权限开关 && 权限状态.截图权限 !== undefined) {
            ui.截图权限开关.checked = 权限状态.截图权限;
            console.log("恢复截图权限开关:", 权限状态.截图权限);
        }

        if (ui.前台服务权限开关 && 权限状态.前台服务权限 !== undefined) {
            ui.前台服务权限开关.checked = 权限状态.前台服务权限;
            console.log("恢复前台服务权限开关:", 权限状态.前台服务权限);
        }

        if (ui.读取权限开关 && 权限状态.读取权限 !== undefined) {
            ui.读取权限开关.checked = 权限状态.读取权限;
            console.log("恢复读取权限开关:", 权限状态.读取权限);
        }

        if (ui.通知访问权限开关 && 权限状态.通知访问权限 !== undefined) {
            ui.通知访问权限开关.checked = 权限状态.通知访问权限;
            console.log("恢复通知访问权限开关:", 权限状态.通知访问权限);
        }

        if (ui.控制台显示开关 && 权限状态.控制台显示 !== undefined) {
            ui.控制台显示开关.checked = 权限状态.控制台显示;
            console.log("恢复控制台显示开关:", 权限状态.控制台显示);
        }

        console.log("UI开关状态恢复完成");

    } catch (e) {
        console.error("恢复UI开关状态失败:", e);
    }
}

/**
 * 恢复功能状态
 * 自动启动之前开启的功能
 * @param {object} 权限状态 - 权限状态对象
 */
function 恢复功能状态(权限状态) {
    try {
        console.log("开始恢复功能状态...");

        // 延迟恢复功能状态，确保UI已完全初始化
        setTimeout(function() {
            try {
                // 恢复悬浮窗功能
                if (权限状态.悬浮窗权限 === true) {
                    console.log("自动恢复悬浮窗功能...");
                    if (floaty.checkPermission()) {
                        启动悬浮球();
                        console.log("✅ 悬浮窗功能已自动恢复");
                    } else {
                        console.warn("悬浮窗系统权限不足，无法自动恢复");
                        // 权限不足时，重置开关状态
                        if (ui.悬浮窗权限开关) {
                            ui.悬浮窗权限开关.checked = false;
                        }
                        配置管理.保存权限状态("悬浮窗权限", false);
                    }
                }

                // 恢复前台服务功能
                if (权限状态.前台服务权限 === true) {
                    console.log("自动恢复前台服务功能...");
                    启动前台服务();
                    console.log("✅ 前台服务功能已自动恢复");
                }

                // 恢复无障碍服务功能
                if (权限状态.无障碍服务 === true) {
                    console.log("检查无障碍服务功能...");
                    if (检查无障碍服务权限()) {
                        启用无障碍服务功能();
                        console.log("✅ 无障碍服务功能已自动恢复");
                    } else {
                        console.warn("无障碍服务系统权限不足，无法自动恢复");
                        // 权限不足时，重置开关状态
                        if (ui.无障碍权限开关) {
                            ui.无障碍权限开关.checked = false;
                        }
                        配置管理.保存权限状态("无障碍服务", false);
                    }
                }

                // 恢复通知访问权限功能
                if (权限状态.通知访问权限 === true) {
                    console.log("检查通知访问权限功能...");
                    if (检查通知访问权限()) {
                        启用通知访问功能();
                        console.log("✅ 通知访问权限功能已自动恢复");
                    } else {
                        console.warn("通知访问系统权限不足，无法自动恢复");
                        // 权限不足时，重置开关状态
                        if (ui.通知访问权限开关) {
                            ui.通知访问权限开关.checked = false;
                        }
                        配置管理.保存权限状态("通知访问权限", false);
                    }
                }

                // 恢复控制台显示功能
                if (权限状态.控制台显示 === true) {
                    console.log("自动恢复控制台显示功能...");
                    显示控制台();
                    console.log("✅ 控制台显示功能已自动恢复");
                }

                console.log("功能状态恢复完成");
                toast("权限状态已恢复");

            } catch (e) {
                console.error("恢复功能状态失败:", e);
            }
        }, 1000); // 延迟1秒执行，确保UI完全初始化

    } catch (e) {
        console.error("恢复功能状态失败:", e);
    }
}

/**
 * 启动悬浮球功能
 * @returns {boolean} 启动是否成功
 */
function 启动悬浮球() {
    try {
        // 检查悬浮窗权限
        if (!floaty.checkPermission()) {
            toast("请先授予悬浮窗权限");
            floaty.requestPermission();
            return false;
        }

        // 加载悬浮球管理系统
        if (!global.悬浮球管理系统) {
            global.悬浮球管理系统 = require('../主页/悬浮球/悬浮球管理系统.js');
        }

        // 启动悬浮球
        if (global.悬浮球管理系统) {
            if (global.悬浮球管理系统.启动()) {
                console.log("悬浮球启动成功");
                return true;
            }
        }

        return false;
    } catch (e) {
        console.error("启动悬浮球失败:", e);
        toast("悬浮球启动失败");
        return false;
    }
}

/**
 * 关闭悬浮球功能
 * @returns {boolean} 关闭是否成功
 */
function 关闭悬浮球() {
    try {
        if (global.悬浮球管理系统) {
            if (global.悬浮球管理系统.停止()) {
                console.log("悬浮球关闭成功");
                return true;
            }
        }
        return false;
    } catch (e) {
        console.error("关闭悬浮球失败:", e);
        toast("悬浮球关闭失败");
        return false;
    }
}

/**
 * 检查悬浮球权限状态
 * @returns {boolean} 权限状态
 */
function 检查悬浮球权限状态() {
    try {
        var 有权限 = floaty.checkPermission();
        var 悬浮球运行中 = false;

        // 安全获取悬浮球状态
        if (global.悬浮球管理系统) {
            try {
                var 状态 = global.悬浮球管理系统.获取状态();
                悬浮球运行中 = 状态 && 状态.悬浮球运行中 === true;
            } catch (e) {
                console.warn("获取悬浮球状态失败:", e);
                悬浮球运行中 = false;
            }
        }

        // 同步开关状态
        if (ui.悬浮窗权限开关) {
            ui.悬浮窗权限开关.checked = 有权限 && 悬浮球运行中;
        }

        // 更新内部状态
        抽屉状态.权限状态.悬浮窗权限 = 有权限 && 悬浮球运行中;

        return 有权限 && 悬浮球运行中;
    } catch (e) {
        console.error("检查悬浮球权限状态失败:", e);
        return false;
    }
}

/**
 * 启动前台服务（基于官方API实现，包含打包兼容性处理）
 * 解决打包后前台服务启动失败的问题
 * @returns {boolean} 启动是否成功
 */
function 启动前台服务() {
    try {
        console.log("启动前台服务...");

        // 首先检查前台服务权限
        if (!检查前台服务权限()) {
            console.warn("前台服务权限不足，使用降级方案");
            return 启动降级前台服务();
        }

        // 基于官方文档的实现方式
        try {
            // 设置通知的标题、内容、图标等参数
            var channel_id = "magic_game_helper.foreground";
            var channel_name = "Magic 游戏助手前台服务通知";
            var content_title = "Magic 游戏助手正在运行中";
            var content_text = "请勿手动移除该通知";
            var ticker = "Magic 游戏助手已启动";

            var manager = context.getSystemService(android.app.Service.NOTIFICATION_SERVICE);
            var notification;

            // 使用默认图标，避免资源找不到的问题
            var icon = android.R.drawable.ic_dialog_info;

            // Android 8.0 及以上版本需要创建通知渠道
            if (device.sdkInt >= 26) {
                var channel = new android.app.NotificationChannel(
                    channel_id,
                    channel_name,
                    android.app.NotificationManager.IMPORTANCE_LOW  // 使用低重要性避免打扰
                );
                channel.enableLights(false);  // 关闭指示灯
                channel.setShowBadge(false);  // 不显示角标
                channel.setSound(null, null); // 静音
                manager.createNotificationChannel(channel);

                // 创建通知对象
                notification = new android.app.Notification.Builder(context, channel_id)
                    .setContentTitle(content_title)
                    .setContentText(content_text)
                    .setWhen(new Date().getTime())
                    .setSmallIcon(icon)
                    .setTicker(ticker)
                    .setOngoing(true)
                    .setAutoCancel(false)
                    .build();
            } else {
                // 创建通知对象（兼容低版本 Android）
                notification = new android.app.Notification.Builder(context)
                    .setContentTitle(content_title)
                    .setContentText(content_text)
                    .setWhen(new Date().getTime())
                    .setSmallIcon(icon)
                    .setTicker(ticker)
                    .setOngoing(true)
                    .setAutoCancel(false)
                    .build();
            }

            // 发送通知
            manager.notify(1001, notification);  // 使用固定ID

            toast("前台服务已启动");
            console.log("前台服务启动成功");

            // 更新状态
            抽屉状态.权限状态.前台服务权限 = true;

            return true;
        } catch (error) {
            console.error("前台服务启动失败:", error);
            console.log("尝试降级方案...");
            return 启动降级前台服务();
        }
    } catch (e) {
        console.error("启动前台服务失败:", e);
        return 启动降级前台服务();
    }
}

/**
 * 检查前台服务权限
 * @returns {boolean} 是否有前台服务权限
 */
function 检查前台服务权限() {
    try {
        // 检查FOREGROUND_SERVICE权限
        var 前台服务权限 = context.checkSelfPermission("android.permission.FOREGROUND_SERVICE");
        var 通知权限 = context.checkSelfPermission("android.permission.POST_NOTIFICATIONS");

        var 有前台服务权限 = 前台服务权限 === android.content.pm.PackageManager.PERMISSION_GRANTED;
        var 有通知权限 = device.sdkInt < 33 || 通知权限 === android.content.pm.PackageManager.PERMISSION_GRANTED;

        console.log("前台服务权限检查:", 有前台服务权限, "通知权限:", 有通知权限);

        return 有前台服务权限 && 有通知权限;
    } catch (e) {
        console.error("权限检查失败:", e);
        return false;
    }
}

/**
 * 启动降级前台服务（仅状态管理）
 * 用于打包后权限不足的情况
 * @returns {boolean} 启动是否成功
 */
function 启动降级前台服务() {
    try {
        console.log("启动降级前台服务（仅状态管理）");

        toast("前台服务已启用（降级模式）");
        console.log("前台服务状态已设置为启用（降级模式）");

        // 更新状态
        抽屉状态.权限状态.前台服务权限 = true;

        return true;
    } catch (e) {
        console.error("降级前台服务启动失败:", e);
        toast("前台服务启动失败");
        return false;
    }
}

/**
 * 停止前台服务（基于官方API实现，包含打包兼容性处理）
 * @returns {boolean} 停止是否成功
 */
function 停止前台服务() {
    try {
        console.log("停止前台服务...");

        // 尝试取消通知
        try {
            var manager = context.getSystemService(android.app.Service.NOTIFICATION_SERVICE);
            // 取消特定ID的通知
            manager.cancel(1001);
            // 也尝试取消所有通知（兼容性）
            manager.cancelAll();
            console.log("前台服务通知已取消");
        } catch (e) {
            console.warn("取消通知失败:", e);
        }

        toast("前台服务已停止");
        console.log("前台服务停止成功");

        // 更新状态
        抽屉状态.权限状态.前台服务权限 = false;

        return true;
    } catch (e) {
        console.error("停止前台服务失败:", e);
        toast("前台服务停止失败");

        // 即使失败也更新状态
        抽屉状态.权限状态.前台服务权限 = false;
        return false;
    }
}

/**
 * 检查前台服务状态
 * @returns {boolean} 前台服务是否运行中
 */
function 检查前台服务状态() {
    try {
        // 检查前台服务运行状态
        // 这里简化实现，实际可能需要检查系统服务状态
        return 抽屉状态.权限状态.前台服务权限;
    } catch (e) {
        console.error("检查前台服务状态失败:", e);
        return false;
    }
}

/**
 * 检查存储权限（基于官方API实现）
 * @returns {boolean} 是否有存储权限
 */
function 检查存储权限() {
    try {
        // 基于官方文档的实现方式

        // 方式1：使用runtime.requestPermissions检查权限
        try {
            // 检查是否已有存储权限
            var 存储权限状态 = context.checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE);

            if (存储权限状态 === android.content.pm.PackageManager.PERMISSION_GRANTED) {
                console.log("存储权限检查：已授权");
                return true;
            }
        } catch (e) {
            console.warn("权限状态检查失败:", e);
        }

        // 方式2：尝试访问外部存储目录（基于files模块）
        try {
            var 外部存储路径 = "/storage/emulated/0/";
            var 可访问 = files.isDir(外部存储路径);
            if (可访问) {
                console.log("存储权限检查：可访问外部存储");
                return true;
            }
        } catch (e) {
            console.warn("外部存储访问检查失败:", e);
        }

        // 方式3：尝试在应用目录创建测试文件
        try {
            var 测试文件路径 = files.path("./test_permission.txt");
            files.write(测试文件路径, "权限测试");
            files.remove(测试文件路径);
            console.log("存储权限检查：应用目录可写");
            return true;
        } catch (e) {
            console.warn("应用目录写入检查失败:", e);
        }

        // 方式4：检查storages模块是否可用
        try {
            var 测试存储 = storages.create("permission_test");
            测试存储.put("test", "value");
            测试存储.remove("test");
            console.log("存储权限检查：storages模块可用");
            return true;
        } catch (e) {
            console.warn("storages模块检查失败:", e);
        }

        console.log("存储权限检查：权限不足");
        return false;
    } catch (e) {
        console.error("检查存储权限失败:", e);
        return false;
    }
}

/**
 * 请求存储权限（基于官方API实现，重构后的逻辑）
 */
function 请求存储权限() {
    try {
        console.log("请求存储权限...");

        // 方式1：使用官方API动态请求权限
        try {
            // 使用runtime.requestPermissions动态申请权限
            runtime.requestPermissions(["android.permission.WRITE_EXTERNAL_STORAGE"]);

            // 延迟检查权限状态
            setTimeout(function() {
                var 权限状态 = 检查存储权限();
                if (权限状态) {
                    // 权限授权成功，开启开关
                    toast("存储权限已授权");
                    if (ui.读取权限开关) {
                        ui.读取权限开关.checked = true;
                    }
                    抽屉状态.权限状态.读取权限 = true;
                    console.log("存储权限已开启");

                    // 验证存储权限功能是否真正可用
                    验证存储权限功能();
                } else {
                    // 权限授权失败，引导手动设置
                    toast("存储权限授权失败，请手动设置");
                    引导手动设置权限();
                }
            }, 2000);

        } catch (e) {
            console.warn("动态权限请求失败:", e);
            引导手动设置权限();
        }

    } catch (e) {
        console.error("请求存储权限失败:", e);
        toast("无法请求存储权限");
    }
}

/**
 * 引导用户手动设置权限
 */
function 引导手动设置权限() {
    try {
        // 显示权限说明对话框
        dialogs.alert("存储权限说明",
            "Magic 游戏助手需要存储权限来：\n\n" +
            "• 保存游戏配置和设置\n" +
            "• 记录运行日志和统计数据\n" +
            "• 备份和恢复用户数据\n\n" +
            "请在接下来的设置页面中授予存储权限。"
        ).then(function() {
            // 使用官方API打开应用设置页面
            try {
                app.startActivity({
                    action: "android.settings.APPLICATION_DETAILS_SETTINGS",
                    data: "package:" + context.getPackageName()
                });
                toast("请在权限设置中开启存储权限");
            } catch (e) {
                console.error("打开设置页面失败:", e);
                toast("请手动到设置中授予存储权限");
            }
        });

    } catch (e) {
        console.error("引导手动设置权限失败:", e);
    }
}

/**
 * 初始化存储权限状态（不自动同步开关）
 * @returns {boolean} 权限状态
 */
function 初始化存储权限状态() {
    try {
        var 有权限 = 检查存储权限();

        // 仅初始化内部状态，不自动同步开关
        // 开关状态由用户手动控制
        if (!抽屉状态.权限状态.hasOwnProperty('读取权限')) {
            抽屉状态.权限状态.读取权限 = false;  // 默认关闭
        }

        console.log("存储权限系统状态:", 有权限 ? "已授权" : "未授权");
        console.log("存储权限开关状态:", 抽屉状态.权限状态.读取权限 ? "开启" : "关闭");
        return 有权限;
    } catch (e) {
        console.error("初始化存储权限状态失败:", e);
        return false;
    }
}



/**
 * 通知访问权限管理系统 - 重构版本
 * 基于AutoXjs官方文档和Android系统API实现
 */

/**
 * 检查通知访问权限状态
 * @returns {boolean} 是否有通知访问权限
 */
function 检查通知访问权限() {
    try {
        console.log("开始检查通知访问权限状态...");

        // 方法1：使用Android官方API检查通知监听器权限
        try {
            var 启用的监听器列表 = android.provider.Settings.Secure.getString(
                context.getContentResolver(),
                "enabled_notification_listeners"
            );

            if (启用的监听器列表) {
                var 当前应用包名 = context.getPackageName();
                console.log("当前应用包名:", 当前应用包名);
                console.log("启用的监听器列表:", 启用的监听器列表);

                // 检查当前应用是否在启用的通知监听器列表中
                var 权限已授权 = 启用的监听器列表.indexOf(当前应用包名) !== -1;

                if (权限已授权) {
                    console.log("✅ 通知访问权限检查：已授权");
                    return true;
                } else {
                    console.log("❌ 通知访问权限检查：未授权");
                    return false;
                }
            } else {
                console.log("❌ 通知访问权限检查：无启用的监听器");
                return false;
            }
        } catch (e) {
            console.error("通知访问权限检查失败:", e);
            return false;
        }
    } catch (e) {
        console.error("检查通知访问权限异常:", e);
        return false;
    }
}

/**
 * 请求通知访问权限
 * 直接跳转到系统设置页面
 */
function 请求通知访问权限() {
    try {
        console.log("开始请求通知访问权限...");

        // 直接跳转到通知访问权限设置页面，不显示说明对话框
        try {
            app.startActivity({
                action: "android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS"
            });
            toast("请在设置中开启Magic游戏助手的通知访问权限");
            console.log("已跳转到通知访问权限设置页面");
        } catch (e) {
            console.error("跳转到设置页面失败:", e);
            toast("无法打开设置页面");
        }

    } catch (e) {
        console.error("请求通知访问权限失败:", e);
        toast("权限申请失败");
    }
}

/**
 * 启用通知访问功能
 * 在权限已授权的情况下启用相关功能
 * @returns {boolean} 启用是否成功
 */
function 启用通知访问功能() {
    try {
        console.log("启用通知访问功能...");

        // 检查权限状态
        if (!检查通知访问权限()) {
            console.error("通知访问权限未授权，无法启用功能");
            toast("请先授予通知访问权限");
            return false;
        }

        // 设置全局状态标记
        if (!global.通知访问服务状态) {
            global.通知访问服务状态 = {};
        }
        global.通知访问服务状态.已启用 = true;
        global.通知访问服务状态.启用时间 = new Date().toLocaleString();

        // 更新内部状态
        抽屉状态.权限状态.通知访问权限 = true;

        console.log("✅ 通知访问功能已启用");
        toast("通知访问功能已启用");

        return true;
    } catch (e) {
        console.error("启用通知访问功能失败:", e);
        toast("启用功能失败: " + e.message);
        return false;
    }
}

/**
 * 停用通知访问功能
 * 关闭相关功能但不撤销系统权限
 * @returns {boolean} 停用是否成功
 */
function 停用通知访问功能() {
    try {
        console.log("停用通知访问功能...");

        // 清除全局状态标记
        if (global.通知访问服务状态) {
            global.通知访问服务状态.已启用 = false;
            global.通知访问服务状态.停用时间 = new Date().toLocaleString();
        }

        // 更新内部状态
        抽屉状态.权限状态.通知访问权限 = false;

        console.log("✅ 通知访问功能已停用");
        toast("通知访问功能已关闭");

        return true;
    } catch (e) {
        console.error("停用通知访问功能失败:", e);
        toast("停用功能失败: " + e.message);
        return false;
    }
}

/**
 * 关闭通知访问权限
 * 基于Android 9.0系统限制，只能跳转到设置页面让用户手动关闭
 */
function 关闭通知访问权限() {
    try {
        console.log("开始关闭通知访问权限...");

        // 检查当前权限状态
        var 当前权限状态 = 检查通知访问权限();

        if (!当前权限状态) {
            // 权限已经关闭，直接停用功能
            console.log("通知访问权限已经关闭，直接停用功能");
            var 停用成功 = 停用通知访问功能();
            if (停用成功) {
                console.log("✅ 通知访问权限关闭完成");
            }
            return;
        }

        // Android系统限制：无法编程方式关闭通知访问权限
        // 只能跳转到设置页面让用户手动关闭
        console.log("Android系统限制：无法编程方式关闭通知访问权限");
        console.log("跳转到设置页面让用户手动关闭...");

        // 先停用应用内功能
        var 停用成功 = 停用通知访问功能();
        if (!停用成功) {
            console.error("停用通知访问功能失败");
            // 回滚开关状态
            if (ui.通知访问权限开关) {
                ui.通知访问权限开关.checked = true;
            }
            抽屉状态.权限状态.通知访问权限 = true;
            return;
        }

        // 跳转到通知访问权限设置页面
        try {
            app.startActivity({
                action: "android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS"
            });
            toast("请在设置中关闭Magic游戏助手的通知访问权限");
            console.log("已跳转到通知访问权限设置页面");

            // 启动权限状态监听器，监听用户是否真正关闭了权限
            启动通知访问权限关闭监听器();

        } catch (e) {
            console.error("跳转到设置页面失败:", e);
            toast("无法打开设置页面");

            // 跳转失败，恢复功能状态
            启用通知访问功能();
            if (ui.通知访问权限开关) {
                ui.通知访问权限开关.checked = true;
            }
            抽屉状态.权限状态.通知访问权限 = true;
        }

    } catch (e) {
        console.error("关闭通知访问权限失败:", e);
        toast("关闭权限失败");

        // 出错时恢复开关状态
        if (ui.通知访问权限开关) {
            ui.通知访问权限开关.checked = true;
        }
        抽屉状态.权限状态.通知访问权限 = true;
    }
}

/**
 * 获取通知访问功能状态
 * @returns {boolean} 功能是否已启用
 */
function 获取通知访问功能状态() {
    try {
        return global.通知访问服务状态 && global.通知访问服务状态.已启用 === true;
    } catch (e) {
        return false;
    }
}

/**
 * 启动通知访问权限监听器
 * 监听应用前后台切换，自动检查权限状态变化
 */
function 启动通知访问权限监听器() {
    try {
        console.log("启动通知访问权限监听器...");

        // 清除之前的监听器（如果存在）
        if (global.通知访问权限监听器) {
            clearInterval(global.通知访问权限监听器);
        }

        // 设置权限检查间隔器
        global.通知访问权限监听器 = setInterval(function() {
            try {
                var 当前权限状态 = 检查通知访问权限();
                var 当前功能状态 = 获取通知访问功能状态();

                // 如果有权限但功能未启用，自动启用
                if (当前权限状态 && !当前功能状态) {
                    console.log("检测到权限已授权，自动启用功能...");
                    var 启用成功 = 启用通知访问功能();

                    if (启用成功 && ui.通知访问权限开关) {
                        ui.通知访问权限开关.checked = true;
                        console.log("✅ 权限授权成功，通知访问功能已自动启用");
                        toast("检测到权限已开启，功能已自动启用");

                        // 停止监听器
                        停止通知访问权限监听器();
                    }
                }

                // 如果没有权限但功能已启用，自动停用
                if (!当前权限状态 && 当前功能状态) {
                    console.log("检测到权限已撤销，自动停用功能...");
                    var 停用成功 = 停用通知访问功能();

                    if (停用成功 && ui.通知访问权限开关) {
                        ui.通知访问权限开关.checked = false;
                        console.log("✅ 权限已撤销，通知访问功能已自动停用");
                        toast("检测到权限已关闭，功能已自动停用");
                    }
                }

            } catch (e) {
                console.error("权限监听器检查失败:", e);
            }
        }, 2000); // 每2秒检查一次

        // 设置监听器超时（30秒后自动停止）
        setTimeout(function() {
            停止通知访问权限监听器();
        }, 30000);

        console.log("通知访问权限监听器已启动，将监听30秒");

    } catch (e) {
        console.error("启动通知访问权限监听器失败:", e);
    }
}

/**
 * 停止通知访问权限监听器
 */
function 停止通知访问权限监听器() {
    try {
        if (global.通知访问权限监听器) {
            clearInterval(global.通知访问权限监听器);
            global.通知访问权限监听器 = null;
            console.log("通知访问权限监听器已停止");
        }
    } catch (e) {
        console.error("停止通知访问权限监听器失败:", e);
    }
}

/**
 * 启动通知访问权限关闭监听器
 * 专门监听用户在设置页面关闭权限的操作
 */
function 启动通知访问权限关闭监听器() {
    try {
        console.log("启动通知访问权限关闭监听器...");

        // 清除之前的监听器（如果存在）
        if (global.通知访问权限关闭监听器) {
            clearInterval(global.通知访问权限关闭监听器);
        }

        // 设置权限检查间隔器
        global.通知访问权限关闭监听器 = setInterval(function() {
            try {
                var 当前权限状态 = 检查通知访问权限();

                // 如果权限已经关闭，确认关闭操作完成
                if (!当前权限状态) {
                    console.log("检测到通知访问权限已关闭");

                    // 确保功能也已停用
                    if (global.通知访问服务状态 && global.通知访问服务状态.已启用) {
                        停用通知访问功能();
                    }

                    // 确保开关状态正确
                    if (ui.通知访问权限开关) {
                        ui.通知访问权限开关.checked = false;
                    }
                    抽屉状态.权限状态.通知访问权限 = false;

                    console.log("✅ 通知访问权限关闭完成");
                    toast("通知访问权限已成功关闭");

                    // 停止监听器
                    停止通知访问权限关闭监听器();
                }

            } catch (e) {
                console.error("权限关闭监听器检查失败:", e);
            }
        }, 2000); // 每2秒检查一次

        // 设置监听器超时（60秒后自动停止）
        setTimeout(function() {
            停止通知访问权限关闭监听器();
        }, 60000);

        console.log("通知访问权限关闭监听器已启动，将监听60秒");

    } catch (e) {
        console.error("启动通知访问权限关闭监听器失败:", e);
    }
}

/**
 * 停止通知访问权限关闭监听器
 */
function 停止通知访问权限关闭监听器() {
    try {
        if (global.通知访问权限关闭监听器) {
            clearInterval(global.通知访问权限关闭监听器);
            global.通知访问权限关闭监听器 = null;
            console.log("通知访问权限关闭监听器已停止");
        }
    } catch (e) {
        console.error("停止通知访问权限关闭监听器失败:", e);
    }
}

/**
 * 检查通知访问权限状态并同步开关
 * 在页面初始化时调用，确保开关状态与实际权限状态一致
 */
function 检查通知访问权限状态并同步开关() {
    try {
        console.log("开始检查通知访问权限状态并同步开关...");

        // 检查系统权限状态
        var 系统权限状态 = 检查通知访问权限();

        // 检查功能启用状态
        var 功能启用状态 = 获取通知访问功能状态();

        // 确定最终状态（需要同时有系统权限和功能启用）
        var 最终状态 = 系统权限状态 && 功能启用状态;

        console.log("系统权限状态:", 系统权限状态 ? "已授权" : "未授权");
        console.log("功能启用状态:", 功能启用状态 ? "已启用" : "未启用");

        // 安全的状态计算 - 确保返回boolean类型
        var 最终状态 = Boolean(系统权限状态 && 功能启用状态);
        console.log("最终开关状态:", 最终状态 ? "开启" : "关闭");

        // 安全的开关设置 - 添加控件存在性和类型检查
        if (ui.通知访问权限开关 && typeof 最终状态 === 'boolean') {
            ui.通知访问权限开关.checked = 最终状态;
        } else {
            console.warn("通知访问权限开关控件不存在或状态类型错误");
        }

        // 更新内部状态
        抽屉状态.权限状态.通知访问权限 = 最终状态;

        // 如果有系统权限但功能未启用，自动启用功能
        if (系统权限状态 && !功能启用状态) {
            console.log("检测到有系统权限但功能未启用，自动启用功能...");
            var 启用成功 = 启用通知访问功能();
            if (启用成功) {
                // 重新更新状态
                if (ui.通知访问权限开关) {
                    ui.通知访问权限开关.checked = true;
                }
                抽屉状态.权限状态.通知访问权限 = true;
                console.log("✅ 通知访问功能已自动启用");
            }
        }

        console.log("通知访问权限状态同步完成");

    } catch (e) {
        console.error("检查通知访问权限状态失败:", e);

        // 出错时设置为安全状态
        if (ui.通知访问权限开关) {
            ui.通知访问权限开关.checked = false;
        }
        抽屉状态.权限状态.通知访问权限 = false;
    }
}

/**
 * 检查截图权限（安全版本，避免卡死）
 * @returns {boolean} 是否有截图权限
 */
function 检查截图权限() {
    try {
        console.log("截图权限检查：使用安全检查方式");

        // 方法1：检查内部状态
        if (抽屉状态.权限状态.截图权限) {
            console.log("截图权限检查：内部状态显示已授权");
            return true;
        }

        // 方法2：尝试安全的权限检查（不调用captureScreen）
        try {
            // 检查是否存在截图相关的全局对象
            if (typeof requestScreenCapture === 'function') {
                console.log("截图权限检查：requestScreenCapture函数可用");
                // 不直接调用，避免弹出权限对话框
                return false; // 默认返回false，让用户主动申请
            }
        } catch (e) {
            console.warn("截图权限检查：函数检查失败", e);
        }

        console.log("截图权限检查：默认返回未授权");
        return false; // 默认返回false，让用户主动申请
    } catch (e) {
        console.error("检查截图权限失败:", e);
        return false;
    }
}

/**
 * 请求截图权限（异步版本，避免卡死）
 */
function 请求截图权限() {
    try {
        console.log("请求截图权限...");
        toast("正在申请截图权限，请稍候...");

        // 使用线程方式申请截图权限，避免阻塞主线程（基于搜索结果优化）
        try {
            if (typeof threads !== 'undefined' && threads.start) {
                // 方法1：使用threads.start()异步申请权限
                console.log("使用threads.start()异步申请截图权限...");

                threads.start(function() {
                    try {
                        console.log("线程中开始申请截图权限...");

                        // 申请截屏权限（在独立线程中执行）
                        var 权限申请结果 = requestScreenCapture();

                        // 使用ui.run()在主线程中更新UI
                        ui.run(function() {
                            if (!权限申请结果) {
                                console.error("截图权限申请失败");
                                toast("截图权限申请失败");

                                // 权限申请失败，回滚开关状态
                                if (ui.截图权限开关) {
                                    ui.截图权限开关.checked = false;
                                }
                                抽屉状态.权限状态.截图权限 = false;
                            } else {
                                console.log("截图权限申请成功");
                                toast("截图权限已获取");

                                // 权限申请成功，同步开关状态
                                if (ui.截图权限开关) {
                                    ui.截图权限开关.checked = true;
                                }
                                抽屉状态.权限状态.截图权限 = true;

                                // 延迟验证权限是否真正可用
                                setTimeout(function() {
                                    try {
                                        var 验证结果 = 检查截图权限();
                                        if (验证结果) {
                                            console.log("✅ 截图权限验证成功");
                                            toast("截图功能已可用");
                                        } else {
                                            console.warn("⚠️ 截图权限验证失败");
                                            toast("截图权限可能不完整");
                                        }
                                    } catch (e) {
                                        console.warn("截图权限验证出错:", e);
                                    }
                                }, 1000);
                            }
                        });
                    } catch (e) {
                        console.error("线程中申请截图权限失败:", e);

                        // 使用ui.run()在主线程中更新UI
                        ui.run(function() {
                            toast("截图权限申请出错: " + e.message);

                            // 出错时回滚开关状态
                            if (ui.截图权限开关) {
                                ui.截图权限开关.checked = false;
                            }
                            抽屉状态.权限状态.截图权限 = false;
                        });
                    }
                });
            } else {
                // 方法2：降级到setTimeout方式
                console.log("threads不可用，降级到setTimeout方式...");
                setTimeout(function() {
                    try {
                        console.log("开始异步申请截图权限...");

                        // 申请截屏权限（这会弹出系统对话框）
                        var 权限申请结果 = requestScreenCapture();

                        if (!权限申请结果) {
                            console.error("截图权限申请失败");
                            toast("截图权限申请失败");

                            // 权限申请失败，回滚开关状态
                            if (ui.截图权限开关) {
                                ui.截图权限开关.checked = false;
                            }
                            抽屉状态.权限状态.截图权限 = false;
                        } else {
                            console.log("截图权限申请成功");
                            toast("截图权限已获取");

                            // 权限申请成功，同步开关状态
                            if (ui.截图权限开关) {
                                ui.截图权限开关.checked = true;
                            }
                            抽屉状态.权限状态.截图权限 = true;
                        }
                    } catch (e) {
                        console.error("异步申请截图权限失败:", e);
                        toast("截图权限申请出错: " + e.message);

                        // 出错时回滚开关状态
                        if (ui.截图权限开关) {
                            ui.截图权限开关.checked = false;
                        }
                        抽屉状态.权限状态.截图权限 = false;
                    }
                }, 100); // 延迟100ms执行，避免阻塞UI
            }
        } catch (e) {
            console.error("启动截图权限申请线程失败:", e);
            toast("截图权限申请启动失败");
        }

        return true; // 立即返回，不等待异步结果
    } catch (e) {
        console.error("请求截图权限失败:", e);
        toast("截图权限申请出错");
        return false;
    }
}



/**
 * 检查截图权限状态并同步开关（安全版本）
 * @param {boolean} 仅检查不同步 - 如果为true，只检查权限不同步开关状态
 * @returns {boolean} 权限状态
 */
function 检查截图权限状态(仅检查不同步) {
    try {
        console.log("开始检查截图权限状态...");
        var 有权限 = 检查截图权限();

        // 只有在明确要求同步时才同步开关状态
        if (!仅检查不同步) {
            // 同步开关状态
            if (ui.截图权限开关) {
                ui.截图权限开关.checked = 有权限;
            }
        }

        // 更新内部状态
        if (!抽屉状态.权限状态.hasOwnProperty('截图权限')) {
            抽屉状态.权限状态.截图权限 = false; // 默认关闭，由用户手动控制
        }

        console.log("截图权限状态:", 有权限 ? "已授权" : "未授权");
        return 有权限;
    } catch (e) {
        console.error("检查截图权限状态失败:", e);
        return false;
    }
}

/**
 * 检查无障碍服务权限（基于AutoXjs官方文档实现）
 * @returns {boolean} 是否有无障碍服务权限
 */
function 检查无障碍服务权限() {
    try {
        console.log("开始检查无障碍服务权限...");
        console.log("auto对象:", typeof auto);
        console.log("auto.service:", auto.service);

        // 使用AutoXjs官方API检查无障碍服务状态
        var 服务状态 = auto.service != null;

        console.log("无障碍服务权限检查结果:", 服务状态 ? "已授权" : "未授权");
        return 服务状态;
    } catch (e) {
        console.error("检查无障碍服务权限失败:", e);
        return false;
    }
}

/**
 * 请求无障碍服务权限（基于AutoXjs官方文档实现）
 * 跳转到系统的无障碍服务设置页面
 */
function 请求无障碍服务权限() {
    try {
        console.log("请求无障碍服务权限...");

        // 使用AutoXjs官方API跳转到无障碍服务设置页面
        app.startActivity({
            action: "android.settings.ACCESSIBILITY_SETTINGS"
        });

        toast("请在设置中开启Magic游戏助手的无障碍服务");
        console.log("已跳转到无障碍服务设置页面");

    } catch (e) {
        console.error("跳转到无障碍服务设置失败:", e);
        toast("无法打开无障碍服务设置");
    }
}

/**
 * 启用无障碍服务功能（基于AutoXjs官方文档实现）
 */
function 启用无障碍服务功能() {
    try {
        if (!检查无障碍服务权限()) {
            toast("无障碍服务未开启，无法使用自动化功能");
            return false;
        }

        console.log("无障碍服务功能已启用");
        toast("无障碍服务功能已启用");

        // 设置全局标记，表示无障碍服务功能已启用
        if (!global.无障碍服务状态) {
            global.无障碍服务状态 = {};
        }
        global.无障碍服务状态.已启用 = true;
        global.无障碍服务状态.启用时间 = new Date().toLocaleString();

        console.log("无障碍服务自动化功能现已可用");

        return true;
    } catch (e) {
        console.error("启用无障碍服务功能失败:", e);
        return false;
    }
}

/**
 * 停用无障碍服务功能（基于AutoXjs官方文档实现）
 */
function 停用无障碍服务功能() {
    try {
        console.log("停用无障碍服务功能...");

        // 清除全局标记
        if (global.无障碍服务状态) {
            global.无障碍服务状态.已启用 = false;
            global.无障碍服务状态.停用时间 = new Date().toLocaleString();
        }

        // 真正关闭无障碍服务（基于官方文档实现）
        try {
            if (auto.service != null) {
                console.log("正在关闭无障碍服务...");
                auto.service.disableSelf();
                console.log("✅ 无障碍服务已关闭");
                toast("无障碍服务已完全关闭");
            } else {
                console.log("无障碍服务未开启，无需关闭");
                toast("无障碍服务未开启");
            }
        } catch (e) {
            console.error("关闭无障碍服务失败:", e);
            toast("关闭无障碍服务失败: " + e.message);
        }

        console.log("无障碍服务功能停用完成");

        return true;
    } catch (e) {
        console.error("停用无障碍服务功能失败:", e);
        return false;
    }
}

/**
 * 获取无障碍服务功能状态
 * @returns {boolean} 功能是否已启用
 */
function 获取无障碍服务功能状态() {
    try {
        return global.无障碍服务状态 && global.无障碍服务状态.已启用 === true;
    } catch (e) {
        return false;
    }
}

/**
 * 检查无障碍服务权限状态并同步开关（基于AutoXjs官方文档实现）
 * @param {boolean} 仅检查不同步 - 如果为true，只检查权限不同步开关状态
 * @returns {boolean} 权限状态
 */
function 检查无障碍服务权限状态(仅检查不同步) {
    try {
        var 有权限 = 检查无障碍服务权限();

        // 只有在明确要求同步时才同步开关状态
        if (!仅检查不同步) {
            // 同步开关状态
            if (ui.无障碍权限开关) {
                ui.无障碍权限开关.checked = 有权限;
            }
        }

        // 更新内部状态
        if (!抽屉状态.权限状态.hasOwnProperty('无障碍服务')) {
            抽屉状态.权限状态.无障碍服务 = false; // 默认关闭，由用户手动控制
        }

        console.log("无障碍服务权限状态:", 有权限 ? "已授权" : "未授权");
        return 有权限;
    } catch (e) {
        console.error("检查无障碍服务权限状态失败:", e);
        return false;
    }
}

/**
 * 验证存储权限功能是否真正可用
 * 通过实际的存储操作来验证功能
 */
function 验证存储权限功能() {
    try {
        console.log("开始验证存储权限功能...");

        // 加载配置管理模块
        var 配置管理 = require('../../存储数据/配置管理.js');

        // 测试1：配置读写功能
        var 测试配置键 = "存储权限测试.功能验证";
        var 测试配置值 = "存储权限功能正常_" + new Date().getTime();

        var 保存成功 = 配置管理.保存配置(测试配置键, 测试配置值);
        if (保存成功) {
            var 读取值 = 配置管理.获取配置(测试配置键);
            if (读取值 === 测试配置值) {
                console.log("✅ 配置读写功能验证成功");

                // 测试2：游戏数据存储功能
                var 测试数据键 = "存储权限测试_数据";
                var 测试数据值 = {
                    测试时间: new Date().toISOString(),
                    测试内容: "存储权限功能验证",
                    测试结果: "成功"
                };

                var 数据保存成功 = 配置管理.保存游戏数据(测试数据键, 测试数据值);
                if (数据保存成功) {
                    var 数据读取值 = 配置管理.获取游戏数据(测试数据键);
                    if (数据读取值 && 数据读取值.测试内容 === "存储权限功能验证") {
                        console.log("✅ 游戏数据存储功能验证成功");

                        // 显示成功提示
                        toast("存储权限已开启，功能验证成功！");

                        // 显示详细的功能说明
                        setTimeout(function() {
                            dialogs.alert("存储权限功能已启用",
                                "✅ 存储权限功能验证成功！\n\n" +
                                "现在可以使用以下功能：\n" +
                                "• 保存和读取游戏配置\n" +
                                "• 记录游戏数据和统计\n" +
                                "• 导出和导入配置文件\n" +
                                "• 保存运行日志和错误信息\n\n" +
                                "所有数据将安全保存在设备存储中。"
                            );
                        }, 1000);

                        // 清理测试数据
                        setTimeout(function() {
                            try {
                                配置管理.保存配置(测试配置键, undefined);
                                配置管理.保存游戏数据(测试数据键, undefined);
                                console.log("测试数据已清理");
                            } catch (e) {
                                console.warn("清理测试数据失败:", e);
                            }
                        }, 3000);

                        return true;
                    }
                }
            }
        }

        // 如果验证失败
        console.error("❌ 存储权限功能验证失败");
        toast("存储权限已开启，但功能验证失败");

        dialogs.alert("存储权限警告",
            "⚠️ 存储权限功能验证失败！\n\n" +
            "可能的原因：\n" +
            "• 存储空间不足\n" +
            "• 权限配置不完整\n" +
            "• 系统限制或安全策略\n\n" +
            "建议重新授权或检查设备存储状态。"
        );

        return false;

    } catch (e) {
        console.error("验证存储权限功能失败:", e);
        toast("存储权限功能验证出错");

        dialogs.alert("存储权限错误",
            "❌ 存储权限功能验证出错！\n\n" +
            "错误信息：" + e.message + "\n\n" +
            "请检查应用权限设置或联系技术支持。"
        );

        return false;
    }
}

/**
 * 检查存储权限状态并同步开关（仅在需要时调用）
 * @returns {boolean} 权限状态
 */
function 检查存储权限状态() {
    try {
        var 有权限 = 检查存储权限();

        // 同步开关状态（仅在特定情况下调用）
        if (ui.读取权限开关) {
            ui.读取权限开关.checked = 有权限;
        }

        // 更新内部状态
        抽屉状态.权限状态.读取权限 = 有权限;

        console.log("存储权限状态:", 有权限 ? "已授权" : "未授权");
        return 有权限;
    } catch (e) {
        console.error("检查存储权限状态失败:", e);
        return false;
    }
}

/**
 * 显示控制台（使用AutoXjs原生API，异步处理）
 * @returns {boolean} 显示是否成功
 */
function 显示控制台() {
    try {
        console.log("准备显示控制台...");

        // 使用异步处理避免阻塞主线程
        if (typeof threads !== 'undefined' && threads.start) {
            // 使用threads.start()异步显示控制台
            console.log("使用threads.start()异步显示控制台...");

            threads.start(function() {
                try {
                    console.log("线程中开始显示控制台...");

                    // 检查console.show方法是否可用
                    if (typeof console.show === 'function') {
                        // 直接显示控制台，不进行任何配置（避免WeakReference错误）
                        console.show();

                        // 使用ui.run()在主线程中更新UI状态
                        ui.run(function() {
                            console.log("✅ 控制台已显示");
                            toast("日志记录控制台已显示");
                        });
                    } else {
                        ui.run(function() {
                            console.warn("console.show() 方法不可用");
                            toast("控制台显示功能不可用");
                        });
                    }
                } catch (e) {
                    console.error("线程中显示控制台失败:", e);
                    ui.run(function() {
                        toast("控制台显示失败: " + e.message);
                    });
                }
            });

            return true;
        } else {
            // 降级到setTimeout方式
            console.log("threads不可用，降级到setTimeout方式...");
            setTimeout(function() {
                try {
                    if (typeof console.show === 'function') {
                        // 跳过位置设置，避免WeakReference错误
                        // if (typeof console.setPosition === 'function') {
                        //     var 屏幕高度 = device.height || 1920;
                        //     var 控制台高度 = 300;
                        //     var 底部位置 = 屏幕高度 - 控制台高度 - 100;
                        //     console.setPosition(50, 底部位置);
                        // }

                        console.show();
                        console.log("✅ 控制台已显示");
                        toast("日志记录控制台已显示");
                    } else {
                        console.warn("console.show() 方法不可用");
                        toast("控制台显示功能不可用");
                    }
                } catch (e) {
                    console.error("异步显示控制台失败:", e);
                    toast("控制台显示失败: " + e.message);
                }
            }, 100);

            return true;
        }
    } catch (e) {
        console.error("显示控制台失败:", e);
        toast("控制台显示失败: " + e.message);
        return false;
    }
}

/**
 * 隐藏控制台（使用AutoXjs原生API，异步处理）
 * @returns {boolean} 隐藏是否成功
 */
function 隐藏控制台() {
    try {
        console.log("准备隐藏控制台...");

        // 使用异步处理避免阻塞主线程
        if (typeof threads !== 'undefined' && threads.start) {
            // 使用threads.start()异步隐藏控制台
            console.log("使用threads.start()异步隐藏控制台...");

            threads.start(function() {
                try {
                    console.log("线程中开始隐藏控制台...");

                    // 检查console.hide方法是否可用
                    if (typeof console.hide === 'function') {
                        console.hide();

                        // 使用ui.run()在主线程中更新UI状态
                        ui.run(function() {
                            console.log("✅ 控制台已隐藏");
                            toast("日志记录控制台已隐藏");
                        });
                    } else {
                        ui.run(function() {
                            console.warn("console.hide() 方法不可用");
                            toast("控制台隐藏功能不可用");
                        });
                    }
                } catch (e) {
                    console.error("线程中隐藏控制台失败:", e);
                    ui.run(function() {
                        toast("控制台隐藏失败: " + e.message);
                    });
                }
            });

            return true;
        } else {
            // 降级到setTimeout方式
            console.log("threads不可用，降级到setTimeout方式...");
            setTimeout(function() {
                try {
                    if (typeof console.hide === 'function') {
                        console.hide();
                        console.log("✅ 控制台已隐藏");
                        toast("日志记录控制台已隐藏");
                    } else {
                        console.warn("console.hide() 方法不可用");
                        toast("控制台隐藏功能不可用");
                    }
                } catch (e) {
                    console.error("异步隐藏控制台失败:", e);
                    toast("控制台隐藏失败: " + e.message);
                }
            }, 100);

            return true;
        }
    } catch (e) {
        console.error("隐藏控制台失败:", e);
        toast("控制台隐藏失败: " + e.message);
        return false;
    }
}

/**
 * 检查控制台状态
 * @returns {boolean} 控制台是否显示
 */
function 检查控制台状态() {
    try {
        // AutoXjs没有直接的API检查控制台状态
        // 使用内部状态管理
        return 抽屉状态.权限状态.控制台显示;
    } catch (e) {
        console.error("检查控制台状态失败:", e);
        return false;
    }
}

/**
 * 检查控制台显示状态并同步开关
 * @returns {boolean} 控制台状态
 */
function 检查控制台显示状态() {
    try {
        console.log("开始检查控制台显示状态...");

        // 获取当前控制台状态
        var 控制台状态 = 检查控制台状态();

        // 同步开关状态
        if (ui.控制台显示开关) {
            ui.控制台显示开关.checked = 控制台状态;
        }

        // 更新内部状态
        if (!抽屉状态.权限状态.hasOwnProperty('控制台显示')) {
            抽屉状态.权限状态.控制台显示 = false; // 默认关闭
        }

        console.log("控制台显示状态:", 控制台状态 ? "已显示" : "已隐藏");
        return 控制台状态;
    } catch (e) {
        console.error("检查控制台显示状态失败:", e);
        return false;
    }
}

// 导出模块功能
module.exports = {
    初始化抽屉逻辑: 初始化抽屉逻辑,
    打开抽屉: 打开抽屉,
    关闭抽屉: 关闭抽屉,
    获取抽屉状态: 获取抽屉状态,
    获取权限状态: 获取权限状态,
    更新权限状态显示: 更新权限状态显示,
    // 无障碍服务控制接口（基于AutoXjs官方文档重新实现）
    检查无障碍服务权限: 检查无障碍服务权限,
    请求无障碍服务权限: 请求无障碍服务权限,
    检查无障碍服务权限状态: 检查无障碍服务权限状态,
    启用无障碍服务功能: 启用无障碍服务功能,
    停用无障碍服务功能: 停用无障碍服务功能,
    获取无障碍服务功能状态: 获取无障碍服务功能状态,
    // 悬浮球控制接口
    启动悬浮球: 启动悬浮球,
    关闭悬浮球: 关闭悬浮球,
    检查悬浮球权限状态: 检查悬浮球权限状态,
    // 前台服务控制接口
    启动前台服务: 启动前台服务,
    停止前台服务: 停止前台服务,
    检查前台服务状态: 检查前台服务状态,
    // 存储权限控制接口
    检查存储权限: 检查存储权限,
    请求存储权限: 请求存储权限,
    初始化存储权限状态: 初始化存储权限状态,
    检查存储权限状态: 检查存储权限状态,
    验证存储权限功能: 验证存储权限功能,
    // 通知访问权限控制接口 - 重构版本
    检查通知访问权限: 检查通知访问权限,
    请求通知访问权限: 请求通知访问权限,
    关闭通知访问权限: 关闭通知访问权限,
    启用通知访问功能: 启用通知访问功能,
    停用通知访问功能: 停用通知访问功能,
    获取通知访问功能状态: 获取通知访问功能状态,
    启动通知访问权限监听器: 启动通知访问权限监听器,
    停止通知访问权限监听器: 停止通知访问权限监听器,
    启动通知访问权限关闭监听器: 启动通知访问权限关闭监听器,
    停止通知访问权限关闭监听器: 停止通知访问权限关闭监听器,
    检查通知访问权限状态并同步开关: 检查通知访问权限状态并同步开关,
    // 截图权限控制接口
    检查截图权限: 检查截图权限,
    请求截图权限: 请求截图权限,
    检查截图权限状态: 检查截图权限状态,
    // 控制台显示控制接口
    显示控制台: 显示控制台,
    隐藏控制台: 隐藏控制台,
    检查控制台状态: 检查控制台状态,
    检查控制台显示状态: 检查控制台显示状态
};

// 应用退出时清理资源
events.on("exit", function() {
    console.log("应用退出，清理抽屉资源...");

    // 清理悬浮球资源
    if (global.悬浮球管理系统 && global.悬浮球管理系统.清理资源) {
        global.悬浮球管理系统.清理资源();
    }

    // 清理通知访问权限监听器
    停止通知访问权限监听器();
    停止通知访问权限关闭监听器();

    // 清理全局状态
    if (global.截图服务状态) {
        delete global.截图服务状态;
    }
    if (global.通知访问服务状态) {
        delete global.通知访问服务状态;
    }
    if (global.通知访问权限监听器) {
        delete global.通知访问权限监听器;
    }
    if (global.通知访问权限关闭监听器) {
        delete global.通知访问权限关闭监听器;
    }

    console.log("抽屉资源清理完成");
});