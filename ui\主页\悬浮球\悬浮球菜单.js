/**
 * Magic悬浮球系统 - 菜单管理模块
 * 负责创建和管理悬浮球的四个菜单图标
 * 包括开始、重启、主页、日志四个功能按钮
 */

// 导入依赖模块
var 通用图标管理器 = require('../../全局样式/通用图标管理器.js');
var 配置管理 = require('../../../存储数据/配置管理.js');

// 脚本控制相关变量
var 脚本控制 = {
    算数脚本线程: null,
    脚本运行中: false
};

// 全局停止标志（供所有模块检查）
global.脚本停止标志 = false;

// 菜单状态管理
var 菜单状态 = {
    菜单已展开: false,
    菜单窗口列表: [],
    菜单配置: {
        开始: { 图标: "播放", 分类: "媒体", 位置: { x: 47, y: -80 } },   // 右上方
        停止: { 图标: "停止", 分类: "媒体", 位置: { x: 95, y: -15 } },   // 右侧
        主页: { 图标: "主页", 分类: "导航", 位置: { x: 80, y: 55 } },    // 右下方
        日志: { 图标: "文档", 分类: "文件", 位置: { x: 20, y: 105 } }    // 下方
    }
};

/**
 * 悬浮球菜单管理模块
 */
var 悬浮球菜单 = {
    
    /**
     * 创建菜单图标
     * @param {String} 菜单名 菜单名称
     * @param {Object} 基础位置 主球的位置
     * @returns {Object} 菜单窗口实例
     */
    创建菜单图标: function(菜单名, 基础位置) {
        try {
            var 配置 = 菜单状态.菜单配置[菜单名];
            if (!配置) {
                console.warn("未知的菜单配置:", 菜单名);
                return null;
            }
            
            // 计算菜单位置
            var 菜单位置 = {
                x: 基础位置.x + 配置.位置.x,
                y: 基础位置.y + 配置.位置.y
            };
            
            // 创建菜单窗口 (基于旧方案尺寸：50dp容器，40dp图标)
            var 菜单窗口 = floaty.window(
                <frame w="50dp" h="50dp" gravity="center">
                    <card id="菜单图标"
                          w="40dp" h="40dp"
                          cardBackgroundColor="#FFFFFF"
                          cardCornerRadius="20dp"
                          cardElevation="2dp"
                          alpha="0.9">

                        <text id={菜单名 + "图标"}
                              text=""
                              textSize="16sp"
                              textColor="#333333"
                              gravity="center"
                              w="*" h="*"/>
                    </card>
                </frame>
            );
            
            if (菜单窗口) {
                // 设置位置
                菜单窗口.setPosition(菜单位置.x, 菜单位置.y);
                
                // 应用图标
                this.应用菜单图标(菜单窗口, 菜单名, 配置);
                
                // 绑定点击事件
                this.绑定菜单事件(菜单窗口, 菜单名);
                
                console.log("菜单图标创建成功:", 菜单名);
                return 菜单窗口;
            }
            
            return null;
        } catch (e) {
            console.error("创建菜单图标失败:", 菜单名, e);
            return null;
        }
    },
    
    /**
     * 应用菜单图标
     * @param {Object} 菜单窗口 菜单窗口实例
     * @param {String} 菜单名 菜单名称
     * @param {Object} 配置 菜单配置
     */
    应用菜单图标: function(菜单窗口, 菜单名, 配置) {
        try {
            var 图标控件ID = 菜单名 + "图标";
            if (菜单窗口[图标控件ID]) {
                // 使用通用图标管理器应用图标
                通用图标管理器.应用悬浮球控件图标(
                    菜单窗口,
                    图标控件ID,
                    配置.分类,
                    配置.图标,
                    {
                        颜色: "#333333",
                        大小: "16sp"
                    }
                );
                console.log("菜单图标应用成功:", 菜单名);
            } else {
                console.warn("菜单图标控件不存在:", 图标控件ID);
            }
        } catch (e) {
            console.error("应用菜单图标失败:", 菜单名, e);
        }
    },
    
    /**
     * 绑定菜单事件
     * @param {Object} 菜单窗口 菜单窗口实例
     * @param {String} 菜单名 菜单名称
     */
    绑定菜单事件: function(菜单窗口, 菜单名) {
        try {
            if (菜单窗口.菜单图标) {
                菜单窗口.菜单图标.click(function() {
                    try {
                        console.log("菜单被点击:", 菜单名);
                        
                        // 震动反馈
                        device.vibrate(50);
                        
                        // 播放点击动画
                        悬浮球菜单.播放菜单动画(菜单窗口.菜单图标);
                        
                        // 执行菜单功能
                        悬浮球菜单.执行菜单功能(菜单名);
                        
                        // 延迟收起菜单
                        setTimeout(function() {
                            悬浮球菜单.收起菜单();
                        }, 300);
                        
                    } catch (e) {
                        console.error("菜单点击事件处理失败:", e);
                    }
                });
                console.log("菜单事件绑定成功:", 菜单名);
            }
        } catch (e) {
            console.error("绑定菜单事件失败:", 菜单名, e);
        }
    },
    
    /**
     * 播放菜单动画
     * @param {Object} 控件 菜单控件
     */
    播放菜单动画: function(控件) {
        try {
            if (控件) {
                控件.animate()
                    .scaleX(0.8)
                    .scaleY(0.8)
                    .setDuration(80)
                    .withEndAction(function() {
                        控件.animate()
                            .scaleX(1.0)
                            .scaleY(1.0)
                            .setDuration(80)
                            .start();
                    })
                    .start();
            }
        } catch (e) {
            console.error("播放菜单动画失败:", e);
        }
    },
    
    /**
     * 展开菜单
     * @param {Object} 主球位置 主球的位置
     * @returns {boolean} 展开是否成功
     */
    展开菜单: function(主球位置) {
        try {
            if (菜单状态.菜单已展开) {
                console.log("菜单已展开，无需重复操作");
                return true;
            }
            
            console.log("开始展开悬浮球菜单...");
            
            // 创建四个菜单图标
            var 菜单名列表 = ["开始", "停止", "主页", "日志"];
            
            for (var i = 0; i < 菜单名列表.length; i++) {
                var 菜单名 = 菜单名列表[i];
                var 菜单窗口 = this.创建菜单图标(菜单名, 主球位置);
                
                if (菜单窗口) {
                    菜单状态.菜单窗口列表.push({
                        名称: 菜单名,
                        窗口: 菜单窗口
                    });
                }
            }
            
            菜单状态.菜单已展开 = true;
            console.log("悬浮球菜单展开成功，共创建", 菜单状态.菜单窗口列表.length, "个菜单");
            return true;
            
        } catch (e) {
            console.error("展开菜单失败:", e);
            return false;
        }
    },
    
    /**
     * 收起菜单
     * @returns {boolean} 收起是否成功
     */
    收起菜单: function() {
        try {
            if (!菜单状态.菜单已展开) {
                console.log("菜单未展开，无需收起");
                return true;
            }
            
            console.log("开始收起悬浮球菜单...");
            
            // 关闭所有菜单窗口
            菜单状态.菜单窗口列表.forEach(function(菜单项) {
                try {
                    if (菜单项.窗口) {
                        菜单项.窗口.close();
                    }
                } catch (e) {
                    console.warn("关闭菜单窗口失败:", 菜单项.名称, e);
                }
            });
            
            // 清空菜单列表
            菜单状态.菜单窗口列表 = [];
            菜单状态.菜单已展开 = false;
            
            console.log("悬浮球菜单收起成功");
            return true;
            
        } catch (e) {
            console.error("收起菜单失败:", e);
            return false;
        }
    },
    
    /**
     * 切换菜单状态
     * @param {Object} 主球位置 主球的位置
     * @returns {boolean} 切换是否成功
     */
    切换菜单状态: function(主球位置) {
        try {
            if (菜单状态.菜单已展开) {
                return this.收起菜单();
            } else {
                return this.展开菜单(主球位置);
            }
        } catch (e) {
            console.error("切换菜单状态失败:", e);
            return false;
        }
    },
    
    /**
     * 执行菜单功能
     * @param {String} 菜单名 菜单名称
     */
    执行菜单功能: function(菜单名) {
        try {
            switch (菜单名) {
                case "开始":
                    this.执行开始功能();
                    break;
                case "停止":
                    this.执行停止功能();
                    break;
                case "主页":
                    this.执行主页功能();
                    break;
                case "日志":
                    this.执行日志功能();
                    break;
                default:
                    console.warn("未知的菜单功能:", 菜单名);
                    toast("功能开发中: " + 菜单名);
            }
        } catch (e) {
            console.error("执行菜单功能失败:", e);
        }
    },
    
    /**
     * 执行开始功能
     */
    执行开始功能: function() {
        try {
            console.log("执行开始功能");

            // 检查脚本是否已经在运行（从配置中读取状态）
            var 配置中的状态 = 配置管理.获取配置('脚本控制.脚本运行中', false);
            if (脚本控制.脚本运行中 || 配置中的状态) {
                toast("⚠️ 脚本已在运行中");
                console.log("脚本已在运行中，无需重复启动");
                return;
            }

            // 启动算数_程序脚本
            console.log("🚀 启动算数_程序脚本...");
            toast("🚀 启动算数脚本");

            // 在主线程中先设置状态
            脚本控制.脚本运行中 = true;
            global.脚本停止标志 = false;  // 重置停止标志
            console.log("✅ 脚本状态已设置为运行中，停止标志已重置");

            脚本控制.算数脚本线程 = threads.start(function() {
                try {
                    console.log("✅ 算数脚本线程已启动");

                    // 导入并执行算数_程序
                    var 算数主脚本 = require('../../../脚本/算数/su_main.js');
                    if (算数主脚本 && typeof 算数主脚本.算数_程序 === 'function') {
                        算数主脚本.算数_程序();
                    } else {
                        console.error("❌ 无法找到算数_程序函数");
                        toast("❌ 算数_程序函数不存在");
                    }
                } catch (e) {
                    console.error("❌ 算数脚本执行失败:", e);
                    toast("❌ 脚本执行失败");
                } finally {
                    // 脚本执行完成，重置状态
                    脚本控制.脚本运行中 = false;
                    脚本控制.算数脚本线程 = null;
                    console.log("📋 算数脚本执行完成，状态已重置");
                }
            });

            console.log("✅ 算数脚本启动成功");

        } catch (e) {
            console.error("执行开始功能失败:", e);
            toast("❌ 启动失败");
            // 重置状态
            脚本控制.脚本运行中 = false;
            脚本控制.算数脚本线程 = null;
        }
    },
    
    /**
     * 执行停止功能
     */
    执行停止功能: function() {
        try {
            console.log("执行停止功能");

            console.log("⏹️ 停止算数_程序脚本...");
            toast("⏹️ 停止脚本");

            // 设置全局停止标志
            global.脚本停止标志 = true;
            console.log("🚩 已设置全局停止标志");

            // 检查脚本是否在运行
            if (!脚本控制.脚本运行中) {
                toast("⚠️ 脚本未在运行");
                console.log("脚本未在运行，无需停止");
                return;
            }

            // 使用正确的线程停止方法
            try {
                if (脚本控制.算数脚本线程) {
                    console.log("🔄 正在中断算数脚本线程...");
                    脚本控制.算数脚本线程.interrupt();
                    console.log("✅ 算数脚本线程已中断");
                } else {
                    console.log("⚠️ 未找到算数脚本线程引用，尝试停止所有子线程");
                    threads.shutDownAll();
                    console.log("✅ 所有子线程已停止");
                }
            } catch (e) {
                console.error("❌ 停止线程失败:", e);
                // 备用方案
                try {
                    threads.shutDownAll();
                    console.log("✅ 备用方案：所有子线程已停止");
                } catch (e2) {
                    console.error("❌ 备用方案也失败:", e2);
                }
            }

            // 重置脚本状态
            脚本控制.脚本运行中 = false;
            脚本控制.算数脚本线程 = null;

            console.log("✅ 脚本已停止，状态已重置");
            toast("✅ 脚本已停止");

        } catch (e) {
            console.error("执行停止功能失败:", e);
            toast("❌ 停止失败");
            // 强制重置状态
            脚本控制.脚本运行中 = false;
            脚本控制.算数脚本线程 = null;
        }
    },

    /**
     * 获取脚本运行状态
     */
    获取脚本状态: function() {
        return {
            运行中: 脚本控制.脚本运行中,
            线程存在: 脚本控制.算数脚本线程 !== null,
            线程活跃: 脚本控制.算数脚本线程 && 脚本控制.算数脚本线程.isAlive()
        };
    },

    /**
     * 强制重置脚本状态
     */
    重置脚本状态: function() {
        try {
            console.log("🔄 强制重置脚本状态...");
            脚本控制.脚本运行中 = false;
            脚本控制.算数脚本线程 = null;
            console.log("✅ 脚本状态已重置");
            toast("✅ 脚本状态已重置");
        } catch (e) {
            console.error("❌ 重置脚本状态失败:", e);
        }
    },

    /**
     * 获取脚本运行状态（用于调试）
     */
    获取脚本状态: function() {
        var 状态 = {
            本地状态: 脚本控制.脚本运行中,
            线程存在: 脚本控制.算数脚本线程 !== null,
            线程活跃: 脚本控制.算数脚本线程 && 脚本控制.算数脚本线程.isAlive(),
            停止标志: global.脚本停止标志 || false
        };

        console.log("📊 当前脚本状态:", JSON.stringify(状态));
        return 状态;
    },

    /**
     * 执行主页功能
     */
    执行主页功能: function() {
        try {
            console.log("执行主页功能");
            toast("🏠 返回主页");
            // 这里集成页面跳转逻辑
        } catch (e) {
            console.error("执行主页功能失败:", e);
        }
    },
    
    /**
     * 执行日志功能
     */
    执行日志功能: function() {
        try {
            console.log("执行日志功能");

            // 显示脚本状态信息
            var 状态 = this.获取脚本状态();
            var 状态文本 = "脚本状态:\n" +
                         "本地状态: " + (状态.本地状态 ? "运行中" : "已停止") + "\n" +
                         "线程存在: " + (状态.线程存在 ? "是" : "否") + "\n" +
                         "线程活跃: " + (状态.线程活跃 ? "是" : "否") + "\n" +
                         "停止标志: " + (状态.停止标志 ? "已设置" : "未设置");

            toast(状态文本);
            console.log("📋 " + 状态文本.replace(/\n/g, ", "));

        } catch (e) {
            console.error("执行日志功能失败:", e);
        }
    },
    
    /**
     * 获取菜单状态
     * @returns {Object} 菜单状态信息
     */
    获取菜单状态: function() {
        return {
            菜单已展开: 菜单状态.菜单已展开,
            菜单数量: 菜单状态.菜单窗口列表.length
        };
    }
};

// 导出模块
module.exports = 悬浮球菜单;
