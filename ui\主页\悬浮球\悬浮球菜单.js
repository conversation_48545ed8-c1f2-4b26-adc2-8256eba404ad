/**
 * Magic悬浮球系统 - 菜单管理模块
 * 负责创建和管理悬浮球的四个菜单图标
 * 包括开始、重启、主页、日志四个功能按钮
 */

// 导入依赖模块
var 通用图标管理器 = require('../../全局样式/通用图标管理器.js');

// 算数程序模块 - 延迟加载，避免循环引用
var 算数程序模块 = null;

/**
 * 安全加载算数程序模块
 * 延迟加载避免循环引用导致的崩溃
 */
function 安全加载算数程序模块() {
    try {
        if (算数程序模块 === null) {
            console.log("🔄 悬浮球：延迟加载算数程序模块...");
            算数程序模块 = require('../../../脚本/算数/su_main.js');
            console.log("✅ 悬浮球：算数程序模块加载成功");
        }
        return 算数程序模块;
    } catch (e) {
        console.error("❌ 悬浮球：算数程序模块加载失败:", e);
        toast("算数程序模块加载失败: " + e.message);
        return null;
    }
}

// 菜单状态管理
var 菜单状态 = {
    菜单已展开: false,
    菜单窗口列表: [],
    菜单配置: {
        开始: { 图标: "播放", 分类: "媒体", 位置: { x: 47, y: -80 } },   // 右上方
        停止: { 图标: "停止", 分类: "媒体", 位置: { x: 95, y: -15 } },   // 右侧
        主页: { 图标: "主页", 分类: "导航", 位置: { x: 80, y: 55 } },    // 右下方
        日志: { 图标: "文档", 分类: "文件", 位置: { x: 20, y: 105 } }    // 下方
    }
};

/**
 * 悬浮球菜单管理模块
 */
var 悬浮球菜单 = {
    
    /**
     * 创建菜单图标
     * @param {String} 菜单名 菜单名称
     * @param {Object} 基础位置 主球的位置
     * @returns {Object} 菜单窗口实例
     */
    创建菜单图标: function(菜单名, 基础位置) {
        try {
            var 配置 = 菜单状态.菜单配置[菜单名];
            if (!配置) {
                console.warn("未知的菜单配置:", 菜单名);
                return null;
            }
            
            // 计算菜单位置
            var 菜单位置 = {
                x: 基础位置.x + 配置.位置.x,
                y: 基础位置.y + 配置.位置.y
            };
            
            // 创建菜单窗口 (基于旧方案尺寸：50dp容器，40dp图标)
            var 菜单窗口 = floaty.window(
                <frame w="50dp" h="50dp" gravity="center">
                    <card id="菜单图标"
                          w="40dp" h="40dp"
                          cardBackgroundColor="#FFFFFF"
                          cardCornerRadius="20dp"
                          cardElevation="2dp"
                          alpha="0.9">

                        <text id={菜单名 + "图标"}
                              text=""
                              textSize="16sp"
                              textColor="#333333"
                              gravity="center"
                              w="*" h="*"/>
                    </card>
                </frame>
            );
            
            if (菜单窗口) {
                // 设置位置
                菜单窗口.setPosition(菜单位置.x, 菜单位置.y);
                
                // 应用图标
                this.应用菜单图标(菜单窗口, 菜单名, 配置);
                
                // 绑定点击事件
                this.绑定菜单事件(菜单窗口, 菜单名);
                
                console.log("菜单图标创建成功:", 菜单名);
                return 菜单窗口;
            }
            
            return null;
        } catch (e) {
            console.error("创建菜单图标失败:", 菜单名, e);
            return null;
        }
    },
    
    /**
     * 应用菜单图标
     * @param {Object} 菜单窗口 菜单窗口实例
     * @param {String} 菜单名 菜单名称
     * @param {Object} 配置 菜单配置
     */
    应用菜单图标: function(菜单窗口, 菜单名, 配置) {
        try {
            var 图标控件ID = 菜单名 + "图标";
            if (菜单窗口[图标控件ID]) {
                // 使用通用图标管理器应用图标
                通用图标管理器.应用悬浮球控件图标(
                    菜单窗口,
                    图标控件ID,
                    配置.分类,
                    配置.图标,
                    {
                        颜色: "#333333",
                        大小: "16sp"
                    }
                );
                console.log("菜单图标应用成功:", 菜单名);
            } else {
                console.warn("菜单图标控件不存在:", 图标控件ID);
            }
        } catch (e) {
            console.error("应用菜单图标失败:", 菜单名, e);
        }
    },
    
    /**
     * 绑定菜单事件
     * @param {Object} 菜单窗口 菜单窗口实例
     * @param {String} 菜单名 菜单名称
     */
    绑定菜单事件: function(菜单窗口, 菜单名) {
        try {
            if (菜单窗口.菜单图标) {
                菜单窗口.菜单图标.click(function() {
                    try {
                        console.log("菜单被点击:", 菜单名);
                        
                        // 震动反馈
                        device.vibrate(50);
                        
                        // 播放点击动画
                        悬浮球菜单.播放菜单动画(菜单窗口.菜单图标);
                        
                        // 执行菜单功能
                        悬浮球菜单.执行菜单功能(菜单名);
                        
                        // 延迟收起菜单
                        setTimeout(function() {
                            悬浮球菜单.收起菜单();
                        }, 300);
                        
                    } catch (e) {
                        console.error("菜单点击事件处理失败:", e);
                    }
                });
                console.log("菜单事件绑定成功:", 菜单名);
            }
        } catch (e) {
            console.error("绑定菜单事件失败:", 菜单名, e);
        }
    },
    
    /**
     * 播放菜单动画
     * @param {Object} 控件 菜单控件
     */
    播放菜单动画: function(控件) {
        try {
            if (控件) {
                控件.animate()
                    .scaleX(0.8)
                    .scaleY(0.8)
                    .setDuration(80)
                    .withEndAction(function() {
                        控件.animate()
                            .scaleX(1.0)
                            .scaleY(1.0)
                            .setDuration(80)
                            .start();
                    })
                    .start();
            }
        } catch (e) {
            console.error("播放菜单动画失败:", e);
        }
    },
    
    /**
     * 展开菜单
     * @param {Object} 主球位置 主球的位置
     * @returns {boolean} 展开是否成功
     */
    展开菜单: function(主球位置) {
        try {
            if (菜单状态.菜单已展开) {
                console.log("菜单已展开，无需重复操作");
                return true;
            }
            
            console.log("开始展开悬浮球菜单...");
            
            // 创建四个菜单图标
            var 菜单名列表 = ["开始", "停止", "主页", "日志"];
            
            for (var i = 0; i < 菜单名列表.length; i++) {
                var 菜单名 = 菜单名列表[i];
                var 菜单窗口 = this.创建菜单图标(菜单名, 主球位置);
                
                if (菜单窗口) {
                    菜单状态.菜单窗口列表.push({
                        名称: 菜单名,
                        窗口: 菜单窗口
                    });
                }
            }
            
            菜单状态.菜单已展开 = true;
            console.log("悬浮球菜单展开成功，共创建", 菜单状态.菜单窗口列表.length, "个菜单");
            return true;
            
        } catch (e) {
            console.error("展开菜单失败:", e);
            return false;
        }
    },
    
    /**
     * 收起菜单
     * @returns {boolean} 收起是否成功
     */
    收起菜单: function() {
        try {
            if (!菜单状态.菜单已展开) {
                console.log("菜单未展开，无需收起");
                return true;
            }
            
            console.log("开始收起悬浮球菜单...");
            
            // 关闭所有菜单窗口
            菜单状态.菜单窗口列表.forEach(function(菜单项) {
                try {
                    if (菜单项.窗口) {
                        菜单项.窗口.close();
                    }
                } catch (e) {
                    console.warn("关闭菜单窗口失败:", 菜单项.名称, e);
                }
            });
            
            // 清空菜单列表
            菜单状态.菜单窗口列表 = [];
            菜单状态.菜单已展开 = false;
            
            console.log("悬浮球菜单收起成功");
            return true;
            
        } catch (e) {
            console.error("收起菜单失败:", e);
            return false;
        }
    },
    
    /**
     * 切换菜单状态
     * @param {Object} 主球位置 主球的位置
     * @returns {boolean} 切换是否成功
     */
    切换菜单状态: function(主球位置) {
        try {
            if (菜单状态.菜单已展开) {
                return this.收起菜单();
            } else {
                return this.展开菜单(主球位置);
            }
        } catch (e) {
            console.error("切换菜单状态失败:", e);
            return false;
        }
    },
    
    /**
     * 执行菜单功能
     * @param {String} 菜单名 菜单名称
     */
    执行菜单功能: function(菜单名) {
        try {
            switch (菜单名) {
                case "开始":
                    this.执行开始功能();
                    break;
                case "停止":
                    this.执行停止功能();
                    break;
                case "主页":
                    this.执行主页功能();
                    break;
                case "日志":
                    this.执行日志功能();
                    break;
                default:
                    console.warn("未知的菜单功能:", 菜单名);
                    toast("功能开发中: " + 菜单名);
            }
        } catch (e) {
            console.error("执行菜单功能失败:", e);
        }
    },
    
    /**
     * 执行开始功能
     */
    执行开始功能: function() {
        try {
            console.log("🚀 悬浮球：准备启动算数程序...");

            // 安全加载算数程序模块
            var 算数程序 = 安全加载算数程序模块();
            if (!算数程序) {
                toast("❌ 算数程序模块加载失败");
                return;
            }

            // 检查脚本是否已在运行
            var 当前状态 = 算数程序.获取脚本状态();
            if (当前状态.运行中) {
                toast("⚠️ 脚本已在运行中");
                console.log("⚠️ 脚本已在运行，当前步骤: " + 当前状态.当前步骤);
                return;
            }

            toast("🚀 启动算数程序...");

            // 使用threads.start()启动算数程序（AutoXjs ozobiozobi标准方式）
            threads.start(function() {
                try {
                    console.log("🎯 悬浮球：开始执行算数程序...");

                    // 重新导入模块避免循环引用
                    var 算数程序 = require('../../../脚本/算数/su_main.js');
                    var 执行结果 = 算数程序.算数_程序();

                    // 脚本执行完成后的反馈（不需要ui.run，toast可以在任何线程调用）
                    if (执行结果) {
                        toast("🎉 算数程序执行完成");
                        console.log("✅ 悬浮球：算数程序执行成功");
                    } else {
                        toast("❌ 算数程序执行失败");
                        console.log("❌ 悬浮球：算数程序执行失败");
                    }

                } catch (e) {
                    console.error("悬浮球：算数程序执行异常:", e);
                    toast("❌ 程序执行异常: " + e.message);
                }
            });

            console.log("✅ 悬浮球：算数程序启动请求已发送");

        } catch (e) {
            console.error("执行开始功能失败:", e);
            toast("❌ 启动失败: " + e.message);
        }
    },
    
    /**
     * 执行停止功能
     */
    执行停止功能: function() {
        try {
            console.log("🛑 悬浮球：准备停止算数程序...");

            // 安全加载算数程序模块
            var 算数程序 = 安全加载算数程序模块();
            if (!算数程序) {
                toast("❌ 算数程序模块加载失败");
                return;
            }

            // 检查脚本是否在运行
            var 当前状态 = 算数程序.获取脚本状态();
            if (!当前状态.运行中) {
                toast("⚠️ 脚本未运行");
                console.log("⚠️ 悬浮球：脚本未在运行");
                return;
            }

            // 发送停止请求
            算数程序.请求停止脚本();
            toast("🛑 停止请求已发送");
            console.log("✅ 悬浮球：算数程序停止请求已发送");

            // 延迟检查停止状态（使用threads.start避免阻塞）
            threads.start(function() {
                sleep(2000); // 等待2秒

                // 重新获取模块引用
                var 算数程序_检查 = require('../../../脚本/算数/su_main.js');
                var 最新状态 = 算数程序_检查.获取脚本状态();
                if (!最新状态.运行中) {
                    toast("✅ 算数程序已停止");
                    console.log("✅ 悬浮球：算数程序已成功停止");
                } else {
                    console.log("⚠️ 悬浮球：算数程序仍在运行，当前步骤: " + 最新状态.当前步骤);
                }
            });

        } catch (e) {
            console.error("执行停止功能失败:", e);
            toast("❌ 停止失败: " + e.message);
        }
    },
    
    /**
     * 执行主页功能
     */
    执行主页功能: function() {
        try {
            console.log("执行主页功能");
            toast("🏠 返回主页");
            // 这里集成页面跳转逻辑
        } catch (e) {
            console.error("执行主页功能失败:", e);
        }
    },
    
    /**
     * 执行日志功能
     */
    执行日志功能: function() {
        try {
            console.log("执行日志功能");
            toast("📋 查看日志");
            // 这里集成日志页面跳转逻辑
        } catch (e) {
            console.error("执行日志功能失败:", e);
        }
    },
    
    /**
     * 获取菜单状态
     * @returns {Object} 菜单状态信息
     */
    获取菜单状态: function() {
        return {
            菜单已展开: 菜单状态.菜单已展开,
            菜单数量: 菜单状态.菜单窗口列表.length
        };
    }
};

// 导出模块
module.exports = 悬浮球菜单;
