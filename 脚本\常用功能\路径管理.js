/**
 * Magic项目路径管理模块
 * 解决AutoXjs应用中相对路径失效的问题
 * 基于AutoXjs ozobiozobi v6.5.8.17
 */

/**
 * 获取项目根目录路径
 * 自动检测项目根目录位置
 */
function 获取项目根目录() {
    try {
        // 方案1：尝试通过当前脚本路径推断
        var 当前脚本路径 = engines.myEngine().getSource().toString();
        console.log("🔍 当前脚本路径:", 当前脚本路径);
        
        if (当前脚本路径.includes("/storage/emulated/0/脚本/magic/")) {
            var 项目根路径 = "/storage/emulated/0/脚本/magic";
            console.log("✅ 通过脚本路径检测到项目根目录:", 项目根路径);
            return 项目根路径;
        }
        
        // 方案2：尝试常见的项目路径
        var 常见路径列表 = [
            "/storage/emulated/0/脚本/magic",
            "/storage/emulated/0/magic",
            "/sdcard/脚本/magic",
            "/sdcard/magic"
        ];
        
        for (var i = 0; i < 常见路径列表.length; i++) {
            var 测试路径 = 常见路径列表[i];
            if (files.exists(测试路径 + "/main.js")) {
                console.log("✅ 通过文件检测找到项目根目录:", 测试路径);
                return 测试路径;
            }
        }
        
        // 方案3：使用files.cwd()获取当前工作目录
        var 工作目录 = files.cwd();
        console.log("🔍 当前工作目录:", 工作目录);
        
        // 检查工作目录是否包含main.js
        if (files.exists(工作目录 + "/main.js")) {
            console.log("✅ 工作目录就是项目根目录:", 工作目录);
            return 工作目录;
        }
        
        // 方案4：默认路径
        var 默认路径 = "/storage/emulated/0/脚本/magic";
        console.log("⚠️ 使用默认项目根目录:", 默认路径);
        return 默认路径;
        
    } catch (e) {
        console.error("❌ 获取项目根目录失败:", e);
        return "/storage/emulated/0/脚本/magic";
    }
}

// 全局项目根目录
var 项目根目录 = 获取项目根目录();

/**
 * 获取assets目录路径
 */
function 获取assets路径() {
    return 项目根目录 + "/assets";
}

/**
 * 获取算数游戏assets路径
 */
function 获取算数游戏assets路径() {
    return 项目根目录 + "/assets/算数游戏";
}

/**
 * 获取新手教程图片路径
 */
function 获取新手教程图片路径() {
    return 项目根目录 + "/assets/算数游戏/新手教程图片";
}

/**
 * 获取广告模板路径
 */
function 获取广告模板路径() {
    return 项目根目录 + "/assets/算数游戏/广告";
}

/**
 * 获取答案数字路径
 */
function 获取答案数字路径() {
    return 项目根目录 + "/assets/算数游戏/答案数字";
}

/**
 * 构建完整图片路径
 * @param {string} 相对路径 - 相对于assets的路径，如"算数游戏/新手教程图片/bonus.png"
 * @returns {string} 完整的绝对路径
 */
function 构建图片路径(相对路径) {
    var 完整路径 = 项目根目录 + "/assets/" + 相对路径;
    console.log("🔗 构建图片路径:", 相对路径, "→", 完整路径);
    return 完整路径;
}

/**
 * 构建新手教程图片路径
 * @param {string} 图片名 - 图片文件名，如"bonus.png"
 * @returns {string} 完整的绝对路径
 */
function 构建新手教程图片路径(图片名) {
    var 完整路径 = 获取新手教程图片路径() + "/" + 图片名;
    console.log("🎓 构建新手教程图片路径:", 图片名, "→", 完整路径);
    return 完整路径;
}

/**
 * 构建广告模板路径
 * @param {string} 子目录 - 广告子目录，如"右广告"
 * @param {string} 图片名 - 图片文件名（可选）
 * @returns {string} 完整的绝对路径
 */
function 构建广告模板路径(子目录, 图片名) {
    var 基础路径 = 获取广告模板路径() + "/" + 子目录;
    if (图片名) {
        var 完整路径 = 基础路径 + "/" + 图片名;
        console.log("📺 构建广告模板路径:", 子目录 + "/" + 图片名, "→", 完整路径);
        return 完整路径;
    }
    console.log("📺 构建广告目录路径:", 子目录, "→", 基础路径);
    return 基础路径;
}

/**
 * 构建答案数字路径
 * @param {string} 数字 - 数字文件名，如"0.png"
 * @returns {string} 完整的绝对路径
 */
function 构建答案数字路径(数字) {
    var 完整路径 = 获取答案数字路径() + "/" + 数字;
    console.log("🔢 构建答案数字路径:", 数字, "→", 完整路径);
    return 完整路径;
}

/**
 * 检查路径是否存在
 * @param {string} 路径 - 要检查的路径
 * @returns {boolean} 路径是否存在
 */
function 检查路径存在(路径) {
    var 存在 = files.exists(路径);
    if (存在) {
        console.log("✅ 路径存在:", 路径);
    } else {
        console.log("❌ 路径不存在:", 路径);
    }
    return 存在;
}

/**
 * 转换旧的相对路径为新的绝对路径
 * @param {string} 旧路径 - 旧的相对路径，如"../../assets/算数游戏/新手教程图片/bonus.png"
 * @returns {string} 新的绝对路径
 */
function 转换路径(旧路径) {
    try {
        // 移除开头的相对路径标识
        var 清理路径 = 旧路径.replace(/^\.\.\/\.\.\//g, "");
        
        // 如果路径以assets开头，直接构建
        if (清理路径.startsWith("assets/")) {
            var 相对路径 = 清理路径.substring(7); // 移除"assets/"
            return 构建图片路径(相对路径);
        }
        
        // 如果路径直接是算数游戏相关
        if (清理路径.startsWith("算数游戏/")) {
            return 构建图片路径(清理路径);
        }
        
        // 默认处理
        return 项目根目录 + "/" + 清理路径;
        
    } catch (e) {
        console.error("❌ 路径转换失败:", 旧路径, e);
        return 旧路径;
    }
}

// 导出模块
module.exports = {
    获取项目根目录: 获取项目根目录,
    获取assets路径: 获取assets路径,
    获取算数游戏assets路径: 获取算数游戏assets路径,
    获取新手教程图片路径: 获取新手教程图片路径,
    获取广告模板路径: 获取广告模板路径,
    获取答案数字路径: 获取答案数字路径,
    构建图片路径: 构建图片路径,
    构建新手教程图片路径: 构建新手教程图片路径,
    构建广告模板路径: 构建广告模板路径,
    构建答案数字路径: 构建答案数字路径,
    检查路径存在: 检查路径存在,
    转换路径: 转换路径,
    项目根目录: 项目根目录
};
