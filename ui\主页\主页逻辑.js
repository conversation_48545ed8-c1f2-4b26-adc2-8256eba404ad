/**
 * Magic 游戏辅助脚本 - 主页逻辑
 * 负责主页的交互逻辑、导航栏功能、抽屉页面管理
 * 基于最新AutoXjs API开发，避免过时API
 */

// 导入依赖模块
var 全局样式 = require('../全局样式/公共样式.js');
var 主页界面 = require('./UI主页页面.js');
var 通用图标管理器 = require('../全局样式/通用图标管理器.js');

// 算数程序模块 - 延迟加载，避免循环引用
var 算数程序模块 = null;

// 主页逻辑状态管理
var 主页状态 = {
    脚本运行中: false,
    当前页面: "主页",
    抽屉已打开: false,
    游戏数据: null
};

/**
 * 安全加载算数程序模块
 * 延迟加载避免循环引用导致的崩溃
 */
function 安全加载算数程序模块() {
    try {
        if (算数程序模块 === null) {
            console.log("🔄 延迟加载算数程序模块...");
            算数程序模块 = require('../../脚本/算数/su_main.js');
            console.log("✅ 算数程序模块加载成功");
        }
        return 算数程序模块;
    } catch (e) {
        console.error("❌ 算数程序模块加载失败:", e);
        显示气泡提示("算数程序模块加载失败: " + e.message, "错误");
        return null;
    }
}

/**
 * 初始化主页逻辑
 */
function 初始化主页逻辑() {
    try {
        console.log("开始初始化主页逻辑...");

        // 初始化现代图标
        初始化现代图标();

        // 注册事件监听器
        注册按钮事件();
        注册导航事件();
        注册抽屉事件();

        // 初始化界面状态
        更新界面状态();

        // 初始化抽屉逻辑
        初始化抽屉逻辑();

        // 初始化悬浮球系统
        初始化悬浮球系统();

        console.log("主页逻辑初始化完成");
        return true;
    } catch (e) {
        console.error("主页逻辑初始化失败:", e);
        toast("主页逻辑初始化失败: " + e.message);
        return false;
    }
}

/**
 * 初始化现代图标系统
 * 使用通用图标管理器统一管理所有图标
 */
function 初始化现代图标() {
    try {
        // 使用全局状态管理器检查是否已初始化
        if (global.状态管理器 && global.状态管理器.检查模块状态("图标系统")) {
            console.log("图标系统已初始化，跳过重复初始化");
            应用主页图标();
            return;
        }

        console.log("开始初始化现代图标系统...");

        // 初始化通用图标系统
        通用图标管理器.初始化图标系统(function(成功, 消息) {
            if (成功) {
                console.log("图标系统初始化成功:", 消息);
                应用主页图标();

                // 标记为已初始化
                if (global.状态管理器) {
                    global.状态管理器.标记模块已初始化("图标系统");
                }
            } else {
                console.warn("图标系统初始化失败:", 消息);
                应用主页图标(); // 仍然应用图标（会使用备用图标）
                toast("使用备用图标显示");
            }
        });

    } catch (e) {
        console.error("初始化图标系统失败:", e);
        toast("图标初始化失败: " + e.message);
    }
}

/**
 * 应用主页图标
 * 使用通用图标管理器统一应用所有图标
 */
function 应用主页图标() {
    try {
        console.log("开始应用主页图标...");

        // 应用导航栏图标
        通用图标管理器.应用导航栏图标({
            脚本图标: { 颜色: "#CCFFFFFF" },
            主页图标: { 颜色: 全局样式.颜色主题.白色 },
            日志图标: { 颜色: "#CCFFFFFF" },
            菜单图标: { 颜色: 全局样式.颜色主题.白色 }
        });

        // 应用操作按钮图标
        通用图标管理器.应用操作按钮图标({
            运行按钮: true,
            停止按钮: true,
            刷新按钮: true,
            删除按钮: true
        });

        // 应用抽屉页面图标 - 使用深绿色确保在浅绿背景上可见
        通用图标管理器.应用图标("无障碍图标", "安全", "盾牌", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        通用图标管理器.应用图标("悬浮窗图标", "天气", "云", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        通用图标管理器.应用图标("截图图标", "媒体", "相机", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        // 新增权限图标
        通用图标管理器.应用图标("前台服务图标", "天气", "闪电", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        通用图标管理器.应用图标("存储权限图标", "文件", "文件夹", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        通用图标管理器.应用图标("通知访问图标", "通信", "铃铛", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        通用图标管理器.应用图标("控制台图标", "设备", "控制台", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        // 应用应用图标 - 生化危险
        通用图标管理器.应用图标("应用图标", "安全", "生化危险", {
            颜色: "#4CAF50",
            大小: "36sp"
        });

        // 应用关于应用图标
        通用图标管理器.应用图标("关于图标", "状态", "信息", {
            颜色: "#2E7D32",
            大小: "14sp"
        });

        通用图标管理器.应用图标("关于箭头", "导航", "前进", {
            颜色: "#66BB6A",
            大小: "14sp"
        });

        console.log("主页图标应用完成");
    } catch (e) {
        console.error("应用主页图标失败:", e);
    }
}





/**
 * 注册按钮事件监听器
 */
function 注册按钮事件() {
    try {
        // 运行按钮事件
        if (ui.运行按钮) {
            ui.运行按钮.on("click", function() {
                显示气泡提示("开始运行脚本");

                if (!主页状态.脚本运行中) {
                    启动脚本();
                } else {
                    显示气泡提示("脚本已在运行中", "警告");
                }
            });
        }

        // 停止按钮事件
        if (ui.停止按钮) {
            ui.停止按钮.on("click", function() {
                显示气泡提示("停止运行脚本");

                if (主页状态.脚本运行中) {
                    停止脚本();
                } else {
                    显示气泡提示("脚本未运行", "警告");
                }
            });
        }

        // 刷新按钮事件
        if (ui.刷新按钮) {
            ui.刷新按钮.on("click", function() {
                显示气泡提示("刷新游戏数据");
                刷新游戏数据();
            });
        }

        // 删除数据按钮事件
        if (ui.删除数据按钮) {
            ui.删除数据按钮.on("click", function() {
                显示气泡提示("删除游戏数据");

                // 显示确认对话框
                dialogs.confirm("确认删除", "确定要删除所有游戏数据吗？此操作不可恢复。")
                    .then(确认 => {
                        if (确认) {
                            删除游戏数据();
                            显示气泡提示("游戏数据已删除", "成功");
                        }
                    })
                    .catch(e => {
                        console.error("删除确认对话框出错:", e);
                    });
            });
        }

        console.log("按钮事件注册完成");
    } catch (e) {
        console.error("注册按钮事件失败:", e);
    }
}

/**
 * 注册导航栏事件监听器
 */
function 注册导航事件() {
    try {
        // 脚本配置标签点击事件
        if (ui.脚本标签) {
            ui.脚本标签.on("click", function() {
                切换到页面("脚本配置");
            });
        }

        // 主页标签点击事件
        if (ui.主页标签) {
            ui.主页标签.on("click", function() {
                切换到页面("主页");
            });
        }

        // 日志标签点击事件
        if (ui.日志标签) {
            ui.日志标签.on("click", function() {
                切换到页面("日志");
            });
        }

        console.log("导航事件注册完成");
    } catch (e) {
        console.error("注册导航事件失败:", e);
    }
}

/**
 * 注册抽屉相关事件
 */
function 注册抽屉事件() {
    try {
        console.log("抽屉事件注册完成");
    } catch (e) {
        console.error("注册抽屉事件失败:", e);
    }
}

/**
 * 初始化抽屉逻辑
 */
function 初始化抽屉逻辑() {
    try {
        var 抽屉逻辑 = require('../菜单抽屉页/侧滑抽屉.js');
        抽屉逻辑.初始化抽屉逻辑("主页");
        console.log("主页抽屉逻辑初始化完成");
    } catch (e) {
        console.error("初始化抽屉逻辑失败:", e);
    }
}

/**
 * 初始化悬浮球系统
 */
function 初始化悬浮球系统() {
    try {
        console.log("开始初始化悬浮球系统...");

        // 预加载悬浮球管理系统到全局变量
        if (!global.悬浮球管理系统) {
            global.悬浮球管理系统 = require('./悬浮球/悬浮球管理系统.js');
        }

        // 初始化悬浮球系统（不自动启动）
        if (global.悬浮球管理系统.初始化()) {
            console.log("悬浮球系统初始化成功");

            // 检查权限状态，如果有权限且抽屉中开关是开启的，则自动启动
            setTimeout(function() {
                检查并同步悬浮球状态();
            }, 1000);
        } else {
            console.warn("悬浮球系统初始化失败");
        }

    } catch (e) {
        console.error("初始化悬浮球系统失败:", e);
    }
}

/**
 * 检查并同步悬浮球状态
 */
function 检查并同步悬浮球状态() {
    try {
        // 检查悬浮窗权限
        var 有权限 = floaty.checkPermission();

        if (有权限) {
            // 检查抽屉中的开关状态
            var 抽屉逻辑 = require('../菜单抽屉页/侧滑抽屉.js');
            var 权限状态 = 抽屉逻辑.获取权限状态();

            if (权限状态.悬浮窗权限) {
                // 如果开关是开启的，启动悬浮球
                if (global.悬浮球管理系统) {
                    global.悬浮球管理系统.启动();
                    console.log("悬浮球自动启动成功");
                }
            }
        }

        // 同步抽屉中的开关状态
        var 抽屉逻辑 = require('../菜单抽屉页/侧滑抽屉.js');
        抽屉逻辑.检查悬浮球权限状态();

    } catch (e) {
        console.error("检查悬浮球状态失败:", e);
    }
}

/**
 * 启动脚本
 */
function 启动脚本() {
    try {
        console.log("🚀 准备启动算数程序...");

        // 安全加载算数程序模块
        var 算数程序 = 安全加载算数程序模块();
        if (!算数程序) {
            显示气泡提示("算数程序模块加载失败", "错误");
            return;
        }

        // 检查脚本是否已在运行
        var 当前状态 = 算数程序.获取脚本状态();
        if (当前状态.运行中) {
            显示气泡提示("脚本已在运行中", "警告");
            console.log("⚠️ 脚本已在运行，当前步骤: " + 当前状态.当前步骤);
            return;
        }

        主页状态.脚本运行中 = true;
        更新脚本状态显示();

        // 使用线程启动算数程序（避免UI线程阻塞）
        threads.start(function() {
            try {
                console.log("🎯 开始执行算数程序...");

                // 重新导入模块避免循环引用
                var 算数程序 = require('../../脚本/算数/su_main.js');
                var 执行结果 = 算数程序.算数_程序();

                // 在UI线程中更新状态
                ui.run(function() {
                    主页状态.脚本运行中 = false;
                    更新脚本状态显示();

                    if (执行结果) {
                        显示气泡提示("🎉 算数程序执行完成", "成功");
                        console.log("✅ 算数程序执行成功");
                    } else {
                        显示气泡提示("❌ 算数程序执行失败", "错误");
                        console.log("❌ 算数程序执行失败");
                    }
                });

            } catch (e) {
                // 在UI线程中更新错误状态
                ui.run(function() {
                    主页状态.脚本运行中 = false;
                    更新脚本状态显示();
                    console.error("算数程序执行异常:", e);
                    显示气泡提示("算数程序执行异常: " + e.message, "错误");
                });
            }
        });

        显示气泡提示("🚀 算数程序启动中...", "成功");
        console.log("✅ 算数程序启动请求已发送");

    } catch (e) {
        主页状态.脚本运行中 = false;
        更新脚本状态显示();
        console.error("启动脚本失败:", e);
        显示气泡提示("脚本启动失败: " + e.message, "错误");
    }
}

/**
 * 停止脚本
 */
function 停止脚本() {
    try {
        console.log("🛑 准备停止算数程序...");

        // 安全加载算数程序模块
        var 算数程序 = 安全加载算数程序模块();
        if (!算数程序) {
            显示气泡提示("算数程序模块加载失败", "错误");
            return;
        }

        // 检查脚本是否在运行
        var 当前状态 = 算数程序.获取脚本状态();
        if (!当前状态.运行中) {
            显示气泡提示("脚本未运行", "警告");
            console.log("⚠️ 脚本未在运行");
            return;
        }

        // 发送停止请求
        算数程序.请求停止脚本();

        主页状态.脚本运行中 = false;
        更新脚本状态显示();

        显示气泡提示("🛑 停止请求已发送", "成功");
        console.log("✅ 算数程序停止请求已发送");

        // 延迟检查停止状态（使用threads.start避免UI阻塞）
        threads.start(function() {
            sleep(2000); // 等待2秒

            // 重新获取模块引用
            var 算数程序_检查 = require('../../脚本/算数/su_main.js');
            var 最新状态 = 算数程序_检查.获取脚本状态();

            ui.run(function() {
                if (!最新状态.运行中) {
                    显示气泡提示("✅ 算数程序已停止", "成功");
                    console.log("✅ 算数程序已成功停止");
                } else {
                    console.log("⚠️ 算数程序仍在运行，当前步骤: " + 最新状态.当前步骤);
                }
            });
        });

    } catch (e) {
        console.error("停止脚本失败:", e);
        显示气泡提示("脚本停止失败: " + e.message, "错误");
    }
}

/**
 * 刷新游戏数据
 */
function 刷新游戏数据() {
    try {
        // 模拟数据刷新
        setTimeout(() => {
            主页状态.游戏数据 = {
                "更新时间": new Date().toLocaleString(),
                "数据状态": "已刷新",
                "游戏等级": Math.floor(Math.random() * 50) + 1,
                "金币数量": Math.floor(Math.random() * 10000) + 1000,
                "经验值": Math.floor(Math.random() * 5000) + 500,
                "任务完成": Math.floor(Math.random() * 20) + 1,
                "在线时长": Math.floor(Math.random() * 120) + 30 + " 分钟"
            };

            主页界面.更新游戏数据(主页状态.游戏数据);
            显示气泡提示("数据刷新完成", "成功");
        }, 1000);

        console.log("开始刷新游戏数据");
    } catch (e) {
        console.error("刷新游戏数据失败:", e);
        显示气泡提示("数据刷新失败: " + e.message, "错误");
    }
}

/**
 * 删除游戏数据
 */
function 删除游戏数据() {
    try {
        主页状态.游戏数据 = null;
        主页界面.更新游戏数据(null);

        // 这里可以添加实际的数据删除逻辑
        // var 配置管理 = require('../../存储数据/配置管理.js');
        // 配置管理.清空游戏数据();

        console.log("游戏数据已删除");
    } catch (e) {
        console.error("删除游戏数据失败:", e);
        显示气泡提示("删除数据失败: " + e.message, "错误");
    }
}

/**
 * 切换到指定页面
 */
function 切换到页面(页面名称) {
    try {
        console.log("从主页切换到页面:", 页面名称);

        // 清理当前页面的事件监听器
        清理主页事件();

        // 更新当前页面状态
        主页状态.当前页面 = 页面名称;

        // 根据页面名称加载对应的页面内容
        switch (页面名称) {
            case "主页":
                // 主页已经是当前页面，无需切换
                console.log("当前已在主页");
                break;
            case "脚本配置":
                console.log("从主页切换到:", 页面名称);
                // 使用ui.layout重新加载脚本配置页面
                var 脚本页面 = require('../脚本配置页/UI脚本页面.js');
                ui.layout(脚本页面.布局);

                // 初始化脚本配置页面逻辑
                var 脚本逻辑 = require('../脚本配置页/脚本逻辑.js');
                if (脚本逻辑.初始化脚本逻辑) {
                    脚本逻辑.初始化脚本逻辑();
                }
                break;
            case "日志":
                console.log("从主页切换到:", 页面名称);
                // 使用ui.layout重新加载日志页面
                var 日志页面 = require('../日志页/UI日志页面.js');
                ui.layout(日志页面.布局);

                // 初始化日志页面逻辑
                var 日志逻辑 = require('../日志页/日志逻辑.js');
                if (日志逻辑.初始化日志逻辑) {
                    日志逻辑.初始化日志逻辑();
                }
                break;
            default:
                console.warn("未知页面:", 页面名称);
                显示气泡提示("未知页面: " + 页面名称, "警告");
                return;
        }

        // 显示切换成功提示
        显示气泡提示("已切换到" + 页面名称 + "页面");

    } catch (e) {
        console.error("切换页面失败:", e);
        显示气泡提示("页面切换失败: " + e.message, "错误");
    }
}

/**
 * 清理主页事件监听器
 */
function 清理主页事件() {
    try {
        console.log("清理主页事件监听器...");

        // 停止正在运行的脚本
        if (主页状态.脚本运行中) {
            console.log("检测到脚本正在运行，准备停止...");
            停止脚本();
        }

        console.log("主页事件清理完成");
    } catch (e) {
        console.error("清理主页事件失败:", e);
    }
}

/**
 * 更新导航栏状态
 */
function 更新导航栏状态(当前页面) {
    try {
        // 重置所有导航图标为非激活状态（半透明白色）
        安全设置控件颜色(ui.脚本图标, "#CCFFFFFF");
        安全设置控件颜色(ui.主页图标, "#CCFFFFFF");
        安全设置控件颜色(ui.日志图标, "#CCFFFFFF");

        // 设置当前页面图标为激活状态（纯白色）
        switch (当前页面) {
            case "脚本配置":
                安全设置控件颜色(ui.脚本图标, 全局样式.颜色主题.白色);
                break;
            case "主页":
                安全设置控件颜色(ui.主页图标, 全局样式.颜色主题.白色);
                break;
            case "日志":
                安全设置控件颜色(ui.日志图标, 全局样式.颜色主题.白色);
                break;
        }
    } catch (e) {
        console.error("更新导航栏状态失败:", e);
    }
}

/**
 * 安全设置控件颜色
 * @param {Object} 控件
 * @param {String} 颜色
 */
function 安全设置控件颜色(控件, 颜色) {
    try {
        if (控件 && 颜色) {
            // 优先使用新API
            if (控件.attr && typeof 控件.attr === 'function') {
                控件.attr("textColor", 颜色);
            } else if (控件.setTextColor && typeof 控件.setTextColor === 'function') {
                控件.setTextColor(colors.parseColor(颜色));
            }
            // 静默处理不支持的方法，避免控制台警告
        }
    } catch (e) {
        // 静默处理错误，避免控制台警告
    }
}

/**
 * 打开菜单页面
 * 使用新的抽屉逻辑显示抽屉
 */
function 打开抽屉页面() {
    try {
        console.log("显示侧滑抽屉...");
        var 抽屉逻辑 = require('../菜单抽屉页/侧滑抽屉.js');
        抽屉逻辑.打开抽屉();
        主页状态.抽屉已打开 = true;
    } catch (e) {
        console.error("打开抽屉页面失败:", e);
        显示气泡提示("打开菜单失败", "错误");
    }
}

/**
 * 关闭抽屉页面
 */
function 关闭抽屉页面() {
    try {
        console.log("关闭抽屉页面");
        主页状态.抽屉已打开 = false;
    } catch (e) {
        console.error("关闭抽屉页面失败:", e);
    }
}

/**
 * 更新界面状态
 */
function 更新界面状态() {
    try {
        更新脚本状态显示();
        更新导航栏状态(主页状态.当前页面);
        主页界面.更新游戏数据(主页状态.游戏数据);
    } catch (e) {
        console.error("更新界面状态失败:", e);
    }
}

/**
 * 更新脚本状态显示
 */
function 更新脚本状态显示() {
    try {
        主页界面.更新脚本状态(主页状态.脚本运行中);
    } catch (e) {
        console.error("更新脚本状态显示失败:", e);
    }
}

/**
 * 显示气泡提示
 */
function 显示气泡提示(消息, 类型) {
    try {
        主页界面.显示提示(消息, 类型);
    } catch (e) {
        console.error("显示气泡提示失败:", e);
        // 备用提示方式
        toast(消息);
    }
}

/**
 * 获取当前页面状态
 */
function 获取页面状态() {
    return 主页状态;
}

/**
 * 设置脚本运行状态
 */
function 设置脚本状态(运行中) {
    主页状态.脚本运行中 = 运行中;
    更新脚本状态显示();
}

// 导出模块功能
module.exports = {
    初始化主页逻辑: 初始化主页逻辑,
    切换到页面: 切换到页面,
    打开抽屉页面: 打开抽屉页面,
    关闭抽屉页面: 关闭抽屉页面,
    更新界面状态: 更新界面状态,
    显示气泡提示: 显示气泡提示,
    获取页面状态: 获取页面状态,
    设置脚本状态: 设置脚本状态,
    启动脚本: 启动脚本,
    停止脚本: 停止脚本,
    刷新游戏数据: 刷新游戏数据,
    删除游戏数据: 删除游戏数据
};