/**
 * Magic 游戏辅助脚本 - 脚本配置页面界面
 * 基于AutoXjs原生UI组件开发
 * 使用统一的#00A843绿色主题
 */

// 导入全局样式
var 全局样式 = require('../全局样式/公共样式.js');

// 脚本配置页面XML布局
var 脚本配置页面布局 = (
    <vertical>
        {/* 顶部导航栏 */}
        <horizontal
            background={全局样式.颜色主题.成功绿}
            h={全局样式.尺寸规范.工具栏高度}
            gravity="center_vertical"
            elevation="4dp">

            <button
                id="菜单按钮"
                background="#00000000"
                textColor={全局样式.颜色主题.白色}
                textSize="22sp"
                w="56dp"
                h="56dp"
                gravity="center"/>

            <text
                id="页面标题"
                text="脚本配置"
                textColor={全局样式.颜色主题.白色}
                textSize={全局样式.尺寸规范.字体超大}
                layout_weight="1"
                gravity="center"
                textStyle="bold"/>

            <View w="56dp" h="56dp"/>
        </horizontal>

        {/* 主要内容区域 */}
        <ScrollView
            layout_weight="1"
            background={全局样式.颜色主题.背景主色}>

            <vertical padding={全局样式.尺寸规范.间距中}>

                {/* 游戏配置卡片 */}
                <card
                    id="游戏配置卡片"
                    cardBackgroundColor={全局样式.颜色主题.背景次色}
                    cardCornerRadius="16dp"
                    cardElevation="2dp"
                    margin="0 0 0 16dp">

                    <vertical>
                        <text
                            text="游戏配置"
                            textSize={全局样式.尺寸规范.字体大}
                            textColor={全局样式.颜色主题.文字主色}
                            fontWeight="bold"
                            padding="16dp 16dp 16dp 8dp"/>

                        {/* 登陆play商店 */}
                        <horizontal
                            padding="16dp"
                            gravity="center_vertical"
                            minHeight="56dp">
                            <text
                                text="登陆play商店"
                                textSize={全局样式.尺寸规范.字体中}
                                textColor={全局样式.颜色主题.文字主色}
                                layout_weight="1"/>
                            <switch
                                id="登陆play商店开关"
                                trackTint="#E0E0E0"
                                thumbTint="#FFFFFF"
                                checkedTrackTint={全局样式.颜色主题.成功绿}
                                checkedThumbTint="#FFFFFF"/>
                        </horizontal>

                        {/* 过游戏教程 */}
                        <horizontal
                            padding="16dp"
                            gravity="center_vertical"
                            minHeight="56dp">
                            <text
                                text="过游戏教程"
                                textSize={全局样式.尺寸规范.字体中}
                                textColor={全局样式.颜色主题.文字主色}
                                layout_weight="1"/>
                            <switch
                                id="过游戏教程开关"
                                trackTint="#E0E0E0"
                                thumbTint="#FFFFFF"
                                checkedTrackTint={全局样式.颜色主题.成功绿}
                                checkedThumbTint="#FFFFFF"/>
                        </horizontal>

                        {/* 自动玩游戏 */}
                        <horizontal
                            padding="16dp"
                            gravity="center_vertical"
                            minHeight="56dp">
                            <text
                                text="首次帐号注册"
                                textSize={全局样式.尺寸规范.字体中}
                                textColor={全局样式.颜色主题.文字主色}
                                layout_weight="1"/>
                            <switch
                                id="首次帐号注册开关"
                                trackTint="#E0E0E0"
                                thumbTint="#FFFFFF"
                                checkedTrackTint={全局样式.颜色主题.成功绿}
                                checkedThumbTint="#FFFFFF"/>
                        </horizontal>

                        {/* 每日领IOTA币 */}
                        <horizontal
                            padding="16dp"
                            gravity="center_vertical"
                            minHeight="56dp">
                            <text
                                text="每日领IOTA币"
                                textSize={全局样式.尺寸规范.字体中}
                                textColor={全局样式.颜色主题.文字主色}
                                layout_weight="1"/>
                            <switch
                                id="每日领币开关"
                                trackTint="#E0E0E0"
                                thumbTint="#FFFFFF"
                                checkedTrackTint={全局样式.颜色主题.成功绿}
                                checkedThumbTint="#FFFFFF"/>
                        </horizontal>
                    </vertical>
                </card>



                {/* 分数控制卡片 */}
                <card
                    id="分数控制卡片"
                    cardBackgroundColor={全局样式.颜色主题.背景次色}
                    cardCornerRadius="16dp"
                    cardElevation="2dp"
                    margin="0 0 0 16dp">

                    <vertical>
                        <text
                            text="分数控制"
                            textSize={全局样式.尺寸规范.字体大}
                            textColor={全局样式.颜色主题.文字主色}
                            fontWeight="bold"
                            padding="16dp 16dp 16dp 8dp"/>

                        {/* 分数到达暂停 */}
                        <text
                            text="分数到达暂停"
                            textSize={全局样式.尺寸规范.字体中}
                            textColor={全局样式.颜色主题.文字主色}
                            padding="16dp 16dp 8dp 16dp"/>

                        {/* 分数输入区域 */}
                        <vertical
                            id="分数输入区域"
                            padding="8dp 16dp 16dp 16dp">
                            <horizontal>
                                <vertical layout_weight="1">
                                    <text
                                        text="最低"
                                        textSize={全局样式.尺寸规范.字体小}
                                        textColor={全局样式.颜色主题.文字次色}
                                        margin="0 0 4dp 4dp"/>
                                    <input
                                        id="最低分数"
                                        hint="100"
                                        inputType="number"
                                        textSize={全局样式.尺寸规范.字体中}
                                        background={全局样式.颜色主题.背景禁用}
                                        radius="8dp"
                                        padding="8dp"/>
                                </vertical>
                                <vertical layout_weight="1" margin="8dp 0 0 0">
                                    <text
                                        text="最高"
                                        textSize={全局样式.尺寸规范.字体小}
                                        textColor={全局样式.颜色主题.文字次色}
                                        margin="0 0 4dp 4dp"/>
                                    <input
                                        id="最高分数"
                                        hint="110"
                                        inputType="number"
                                        textSize={全局样式.尺寸规范.字体中}
                                        background={全局样式.颜色主题.背景禁用}
                                        radius="8dp"
                                        padding="8dp"/>
                                </vertical>
                                <text
                                    text="分"
                                    textSize={全局样式.尺寸规范.字体小}
                                    textColor={全局样式.颜色主题.文字次色}
                                    gravity="center"
                                    margin="8dp 0 0 0"
                                    layout_width="40dp"/>
                            </horizontal>
                        </vertical>
                    </vertical>
                </card>


            </vertical>
        </ScrollView>

        {/* 底部操作按钮 */}
        <horizontal
            padding={全局样式.尺寸规范.间距中}>

            <button
                id="保存配置"
                text="保存配置"
                style="Widget.AppCompat.Button.Colored"
                backgroundTint="#4CAF50"
                textColor="#FFFFFF"
                textSize="16sp"
                layout_weight="1"
                margin="0 0 8dp 0"
                h="48dp"
                fontWeight="bold"/>

            <button
                id="重置配置"
                text="重置配置"
                style="Widget.AppCompat.Button.Colored"
                backgroundTint="#4CAF50"
                textColor="#FFFFFF"
                textSize="16sp"
                layout_weight="1"
                margin="8dp 0 0 0"
                h="48dp"
                fontWeight="bold"/>
        </horizontal>

        {/* 底部导航栏 */}
        <horizontal
            background={全局样式.颜色主题.成功绿}
            h="50dp"
            elevation="8dp">

            <vertical
                id="脚本标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="脚本图标"
                    textSize="20sp"
                    textColor={全局样式.颜色主题.白色}
                    gravity="center"/>
            </vertical>

            <vertical
                id="主页标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="主页图标"
                    textSize="20sp"
                    textColor="#CCFFFFFF"
                    gravity="center"/>
            </vertical>

            <vertical
                id="日志标签"
                layout_weight="1"
                gravity="center"
                clickable="true"
                padding="8dp">
                <text
                    id="日志图标"
                    textSize="20sp"
                    textColor="#CCFFFFFF"
                    gravity="center"/>
            </vertical>
        </horizontal>
    </vertical>
);

// 导出脚本配置页面布局和相关功能
module.exports = {
    // 布局定义
    布局: 脚本配置页面布局,

    // 加载配置数据到界面
    加载配置: function() {
        try {
            var 配置管理 = require('../../存储数据/配置管理.js');

            // 加载游戏配置
            ui.登陆play商店开关.setChecked(配置管理.获取配置('游戏配置.登陆play商店', false));
            ui.过游戏教程开关.setChecked(配置管理.获取配置('游戏配置.过游戏教程', false));
            ui.首次帐号注册开关.setChecked(配置管理.获取配置('游戏配置.首次帐号注册', false));
            ui.自动玩游戏开关.setChecked(配置管理.获取配置('游戏配置.自动玩游戏', false));

            // 加载操作配置 - 已删除UI界面，保留数据处理逻辑
            // ui.左键X坐标.setText(配置管理.获取配置('操作配置.左键设置.X坐标', 0).toString());
            // ui.左键Y坐标.setText(配置管理.获取配置('操作配置.左键设置.Y坐标', 0).toString());
            // ui.左键宽度.setText(配置管理.获取配置('操作配置.左键设置.宽度', 100).toString());
            // ui.左键高度.setText(配置管理.获取配置('操作配置.左键设置.高度', 100).toString());

            // ui.右键X坐标.setText(配置管理.获取配置('操作配置.右键设置.X坐标', 0).toString());
            // ui.右键Y坐标.setText(配置管理.获取配置('操作配置.右键设置.Y坐标', 0).toString());
            // ui.右键宽度.setText(配置管理.获取配置('操作配置.右键设置.宽度', 100).toString());
            // ui.右键高度.setText(配置管理.获取配置('操作配置.右键设置.高度', 100).toString());

            // 加载分数控制配置
            ui.最低分数.setText(配置管理.获取配置('分数控制.最低分数', 100).toString());
            ui.最高分数.setText(配置管理.获取配置('分数控制.最高分数', 110).toString());

            // 加载广告配置 - 已删除UI界面，保留数据处理逻辑
            // ui.自动看广告开关.setChecked(配置管理.获取配置('广告配置.自动看广告', false));
            // ui.广告左X坐标.setText(配置管理.获取配置('广告配置.左区域.X坐标', 0).toString());
            // ui.广告左Y坐标.setText(配置管理.获取配置('广告配置.左区域.Y坐标', 0).toString());
            // ui.广告左宽度.setText(配置管理.获取配置('广告配置.左区域.宽度', 100).toString());
            // ui.广告左高度.setText(配置管理.获取配置('广告配置.左区域.高度', 100).toString());

            // ui.广告右X坐标.setText(配置管理.获取配置('广告配置.右区域.X坐标', 0).toString());
            // ui.广告右Y坐标.setText(配置管理.获取配置('广告配置.右区域.Y坐标', 0).toString());
            // ui.广告右宽度.setText(配置管理.获取配置('广告配置.右区域.宽度', 100).toString());
            // ui.广告右高度.setText(配置管理.获取配置('广告配置.右区域.高度', 100).toString());

        } catch (e) {
            console.error("加载配置失败:", e);
        }
    },

    // 保存配置数据
    保存配置: function() {
        try {
            var 配置管理 = require('../../存储数据/配置管理.js');

            // 保存游戏配置
            配置管理.保存配置('游戏配置.登陆play商店', ui.登陆play商店开关.isChecked());
            配置管理.保存配置('游戏配置.过游戏教程', ui.过游戏教程开关.isChecked());
            配置管理.保存配置('游戏配置.首次帐号注册', ui.首次帐号注册开关.isChecked());
            配置管理.保存配置('游戏配置.自动玩游戏', ui.自动玩游戏开关.isChecked());

            // 保存分数控制配置
            配置管理.保存配置('分数控制.最低分数', parseInt(ui.最低分数.getText()) || 100);
            配置管理.保存配置('分数控制.最高分数', parseInt(ui.最高分数.getText()) || 110);

            return true;
        } catch (e) {
            console.error("保存配置失败:", e);
            return false;
        }
    },

    // 重置配置到默认值
    重置配置: function() {
        try {
            var 配置管理 = require('../../存储数据/配置管理.js');

            // 重置游戏配置
            配置管理.重置配置('游戏配置');
            配置管理.重置配置('操作配置');
            配置管理.重置配置('分数控制');
            配置管理.重置配置('广告配置');

            // 重新加载界面
            this.加载配置();

            return true;
        } catch (e) {
            console.error("重置配置失败:", e);
            return false;
        }
    },

    // 显示气泡提示
    显示提示: function(消息, 类型) {
        try {
            if (类型 === "成功") {
                toast("成功: " + 消息);
            } else if (类型 === "错误") {
                toast("错误: " + 消息);
            } else if (类型 === "警告") {
                toast("警告: " + 消息);
            } else {
                toast(消息);
            }
        } catch (e) {
            console.error("显示提示失败:", e);
        }
    },

    // 获取分数控制配置
    获取分数控制: function() {
        try {
            return {
                最低分数: parseInt(ui.最低分数.getText()) || 100,
                最高分数: parseInt(ui.最高分数.getText()) || 110
            };
        } catch (e) {
            console.error("获取分数控制失败:", e);
            return { 最低分数: 100, 最高分数: 110 };
        }
    },


};