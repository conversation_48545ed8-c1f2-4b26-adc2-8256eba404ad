Device connected: Device HUAWEI HMA-AL00(adb: emulator-5556)
00:35:09.708/V: 开始运行[/storage/emulated/0/脚本/magic/main.js]
00:35:09.825/D: 开始初始化简洁日志管理器...
00:35:09.834/D: ✅ 控制台配置修复完成
00:35:09.940/D: ✅ 简洁日志管理器初始化成功
00:35:10.070/D: ==================================================
00:35:10.072/D: Magic 游戏辅助脚本启动中...
00:35:10.074/D: ==================================================
00:35:10.076/D: 1. 初始化简洁日志管理器...
00:35:10.078/D: 日志管理器已初始化，跳过重复初始化
00:35:10.079/D: ✅ 简洁日志管理器初始化完成
00:35:10.080/D: 1. 初始化Assets资源...
00:35:10.082/D: 开始初始化Assets资源到存储目录...
00:35:10.208/D: Assets目标路径: /storage/emulated/0/magic/assets/
00:35:10.209/D: Assets目录已存在，跳过复制
00:35:10.210/D: 2. 初始化配置管理...
00:35:10.210/D: 初始化配置管理模块...
00:35:10.218/D: 配置管理模块初始化完成
00:35:10.220/D: 2. 设置UI布局...
00:35:10.843/D: 3. 初始化逻辑模块...
00:35:10.845/D: 开始初始化主页逻辑...
00:35:10.845/D: 开始初始化现代图标系统...
00:35:10.847/D: 初始化通用图标系统...
00:35:10.858/D: 字体加载成功: FontAwesome
00:35:10.859/D: FontAwesome字体加载成功
00:35:10.862/D: 字体加载成功: Roboto
00:35:10.864/D: Roboto字体加载成功
00:35:10.867/D: 字体加载成功: RobotoBold
00:35:10.868/D: RobotoBold字体加载成功
00:35:10.870/D: 图标系统初始化成功: FontAwesome、Roboto、RobotoBold字体加载成功
00:35:10.872/D: 开始应用主页图标...
00:35:10.880/D: 主页图标应用完成
00:35:10.883/D: 按钮事件注册完成
00:35:10.886/D: 导航事件注册完成
00:35:10.888/D: 抽屉事件注册完成
00:35:10.907/D: 初始化通用抽屉逻辑... 主页
00:35:10.909/D: 菜单按钮事件绑定成功
00:35:10.910/D: 遮罩点击事件绑定成功
00:35:10.912/D: 读取权限开关事件绑定成功
00:35:10.914/D: 悬浮窗权限开关事件绑定成功
00:35:10.916/D: 前台服务权限开关事件绑定成功
00:35:10.918/D: 检查通知访问权限开关控件: 找到
00:35:10.919/D: 开始绑定通知访问权限开关事件...
00:35:10.920/D: 通知访问权限开关事件绑定成功
00:35:10.922/D: 检查截图权限开关控件: 找到
00:35:10.923/D: 开始绑定截图权限开关事件...
00:35:10.923/D: 截图权限开关事件绑定成功
00:35:10.934/D: 检查无障碍权限开关控件: 找到
00:35:10.938/D: 开始绑定无障碍权限开关事件...
00:35:10.939/D: 无障碍服务开关事件绑定成功
00:35:10.941/D: 检查控制台显示开关控件: 找到
00:35:10.944/D: 开始绑定控制台显示开关事件...
00:35:10.946/D: 控制台显示开关事件绑定成功
00:35:10.948/D: 权限开关事件绑定完成
00:35:10.949/D: 导航项事件绑定完成
00:35:10.950/D: 开始恢复权限状态...
00:35:10.966/D: 从配置文件读取的权限状态: {
  "无障碍服务": false,
  "悬浮窗权限": true,
  "截图权限": false,
  "前台服务权限": false,
  "读取权限": false,
  "通知访问权限": true,
  "控制台显示": false
}
00:35:10.968/D: 恢复权限状态: 无障碍服务 = false
00:35:10.970/D: 恢复权限状态: 悬浮窗权限 = true
00:35:10.971/D: 恢复权限状态: 截图权限 = false
00:35:10.973/D: 恢复权限状态: 前台服务权限 = false
00:35:10.975/D: 恢复权限状态: 读取权限 = false
00:35:10.976/D: 恢复权限状态: 通知访问权限 = true
00:35:10.978/D: 恢复权限状态: 控制台显示 = false
00:35:10.980/D: 开始恢复UI开关状态...
00:35:10.982/D: 恢复无障碍权限开关: false
00:35:10.987/D: 配置已保存: 权限状态.悬浮窗权限 = true
00:35:10.989/D: 悬浮球权限: 已开启
00:35:11.006/D: 开始初始化悬浮球系统...
00:35:11.023/D: 模块加载成功: XML悬浮球
00:35:11.040/D: 模块加载成功: 悬浮球事件
00:35:11.049/D: 模块加载成功: 悬浮球菜单
00:35:11.060/D: 模块加载成功: 通用图标管理器
00:35:11.064/D: 初始化通用图标系统...
00:35:11.069/D: 字体加载成功: FontAwesome
00:35:11.070/D: FontAwesome字体加载成功
00:35:11.072/D: 字体加载成功: Roboto
00:35:11.073/D: Roboto字体加载成功
00:35:11.074/D: 字体加载成功: RobotoBold
00:35:11.075/D: RobotoBold字体加载成功
00:35:11.076/D: 图标系统初始化成功: FontAwesome、Roboto、RobotoBold字体加载成功
00:35:11.078/D: 悬浮球系统初始化完成
00:35:11.078/D: 菜单未展开，无需收起
00:35:11.079/D: 所有悬浮球事件已解绑
00:35:11.080/D: 悬浮球停止成功
00:35:11.081/D: 开始创建XML原生悬浮球...
00:35:11.148/D: 悬浮球图标应用成功: 生化危险图标 安全.生化危险
00:35:11.149/D: 生化危险图标应用成功
00:35:11.150/D: 悬浮球拖拽事件绑定成功
00:35:11.151/D: XML悬浮球创建成功
00:35:11.151/D: 悬浮球显示成功
00:35:11.152/D: 主球点击事件绑定成功
00:35:11.153/D: 悬浮球启动成功
00:35:11.154/D: 悬浮球启动成功
00:35:11.154/D: 恢复悬浮窗权限开关: true
00:35:11.155/D: 恢复截图权限开关: false
00:35:11.156/D: 恢复前台服务权限开关: false
00:35:11.157/D: 恢复读取权限开关: false
00:35:11.158/D: 通知访问权限开关被点击，状态: 开启
00:35:11.159/D: 用户要求开启通知访问权限...
00:35:11.160/D: 开始检查通知访问权限状态...
00:35:11.165/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:11.165/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:11.166/D: ✅ 通知访问权限检查：已授权
00:35:11.167/D: 启用通知访问功能...
00:35:11.167/D: 开始检查通知访问权限状态...
00:35:11.168/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:11.169/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:11.170/D: ✅ 通知访问权限检查：已授权
00:35:11.186/D: ✅ 通知访问功能已启用
00:35:11.189/D: 配置已保存: 权限状态.通知访问权限 = true
00:35:11.189/D: ✅ 通知访问权限已开启
00:35:11.190/D: 恢复通知访问权限开关: true
00:35:11.191/D: 恢复控制台显示开关: false
00:35:11.192/D: UI开关状态恢复完成
00:35:11.193/D: 开始恢复功能状态...
00:35:11.195/D: 权限状态恢复完成
00:35:11.195/D: 开始更新权限状态显示（Android 9.0优化）...
00:35:11.196/D: 开始检查无障碍服务权限...
00:35:11.197/D: auto对象: function
00:35:11.203/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:11.204/D: 无障碍服务权限检查结果: 已授权
00:35:11.205/D: 无障碍服务权限状态: 已授权
00:35:11.206/D: 2. 检查悬浮球权限状态...
00:35:11.207/D: 3. 检查前台服务权限状态...
00:35:11.208/D: 4. 初始化存储权限状态...
00:35:11.226/D: 存储权限检查：已授权
00:35:11.227/D: 存储权限系统状态: 已授权
00:35:11.228/D: 存储权限开关状态: 关闭
00:35:11.230/D: 5. 检查通知访问权限状态...
00:35:11.232/D: 开始检查通知访问权限状态并同步开关...
00:35:11.232/D: 开始检查通知访问权限状态...
00:35:11.234/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:11.235/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:11.237/D: ✅ 通知访问权限检查：已授权
00:35:11.238/D: 系统权限状态: 已授权
00:35:11.240/D: 功能启用状态: 已启用
00:35:11.242/D: 最终开关状态: 开启
00:35:11.244/D: 通知访问权限状态同步完成
00:35:11.246/D: 6. 检查截图权限状态...
00:35:11.247/D: 开始检查截图权限状态...
00:35:11.249/D: 截图权限检查：使用安全检查方式
00:35:11.250/D: 截图权限检查：requestScreenCapture函数可用
00:35:11.251/D: 截图权限状态: 未授权
00:35:11.252/D: 7. 检查控制台显示状态...
00:35:11.252/D: 开始检查控制台显示状态...
00:35:11.255/D: 控制台显示状态: 已隐藏
00:35:11.256/D: 权限状态显示更新完成（Android 9.0优化）
00:35:11.261/D: 通用抽屉逻辑初始化完成
00:35:11.264/D: 主页抽屉逻辑初始化完成
00:35:11.266/D: 开始初始化悬浮球系统...
00:35:11.268/D: 开始初始化悬浮球系统...
00:35:11.270/D: 初始化通用图标系统...
00:35:11.272/D: 字体加载成功: FontAwesome
00:35:11.274/D: FontAwesome字体加载成功
00:35:11.277/D: 字体加载成功: Roboto
00:35:11.277/D: Roboto字体加载成功
00:35:11.281/D: 字体加载成功: RobotoBold
00:35:11.282/D: RobotoBold字体加载成功
00:35:11.285/D: 图标系统初始化成功: FontAwesome、Roboto、RobotoBold字体加载成功
00:35:11.286/D: 悬浮球系统初始化完成
00:35:11.287/D: 悬浮球系统初始化成功
00:35:11.288/D: 主页逻辑初始化完成
00:35:11.288/D: 开始初始化日志逻辑...
00:35:11.289/D: 开始初始化日志页面现代图标...
00:35:11.292/D: 日志页面现代图标初始化完成
00:35:11.295/D: 日志页面按钮事件注册完成
00:35:11.298/D: 日志页面导航事件注册完成
00:35:11.316/D: 初始化通用抽屉逻辑... 日志
00:35:11.317/D: 菜单按钮事件绑定成功
00:35:11.319/D: 遮罩点击事件绑定成功
00:35:11.320/D: 读取权限开关事件绑定成功
00:35:11.322/D: 悬浮窗权限开关事件绑定成功
00:35:11.323/D: 前台服务权限开关事件绑定成功
00:35:11.324/D: 检查通知访问权限开关控件: 找到
00:35:11.324/D: 开始绑定通知访问权限开关事件...
00:35:11.326/D: 通知访问权限开关事件绑定成功
00:35:11.328/D: 检查截图权限开关控件: 找到
00:35:11.329/D: 开始绑定截图权限开关事件...
00:35:11.331/D: 截图权限开关事件绑定成功
00:35:11.333/D: 检查无障碍权限开关控件: 找到
00:35:11.333/D: 开始绑定无障碍权限开关事件...
00:35:11.334/D: 无障碍服务开关事件绑定成功
00:35:11.336/D: 检查控制台显示开关控件: 找到
00:35:11.338/D: 开始绑定控制台显示开关事件...
00:35:11.339/D: 控制台显示开关事件绑定成功
00:35:11.341/D: 权限开关事件绑定完成
00:35:11.343/D: 导航项事件绑定完成
00:35:11.344/D: 开始恢复权限状态...
00:35:11.348/D: 从配置文件读取的权限状态: {
  "无障碍服务": false,
  "悬浮窗权限": true,
  "截图权限": false,
  "前台服务权限": false,
  "读取权限": false,
  "通知访问权限": true,
  "控制台显示": false
}
00:35:11.349/D: 恢复权限状态: 无障碍服务 = false
00:35:11.349/D: 恢复权限状态: 悬浮窗权限 = true
00:35:11.350/D: 恢复权限状态: 截图权限 = false
00:35:11.351/D: 恢复权限状态: 前台服务权限 = false
00:35:11.353/D: 恢复权限状态: 读取权限 = false
00:35:11.353/D: 恢复权限状态: 通知访问权限 = true
00:35:11.354/D: 恢复权限状态: 控制台显示 = false
00:35:11.355/D: 开始恢复UI开关状态...
00:35:11.356/D: 恢复无障碍权限开关: false
00:35:11.357/D: 恢复悬浮窗权限开关: true
00:35:11.358/D: 恢复截图权限开关: false
00:35:11.359/D: 恢复前台服务权限开关: false
00:35:11.359/D: 恢复读取权限开关: false
00:35:11.360/D: 恢复通知访问权限开关: true
00:35:11.361/D: 恢复控制台显示开关: false
00:35:11.362/D: UI开关状态恢复完成
00:35:11.363/D: 开始恢复功能状态...
00:35:11.364/D: 权限状态恢复完成
00:35:11.364/D: 开始更新权限状态显示（Android 9.0优化）...
00:35:11.365/D: 开始检查无障碍服务权限...
00:35:11.366/D: auto对象: function
00:35:11.370/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:11.371/D: 无障碍服务权限检查结果: 已授权
00:35:11.372/D: 无障碍服务权限状态: 已授权
00:35:11.372/D: 2. 检查悬浮球权限状态...
00:35:11.376/D: 3. 检查前台服务权限状态...
00:35:11.377/D: 4. 初始化存储权限状态...
00:35:11.386/D: 存储权限检查：已授权
00:35:11.387/D: 存储权限系统状态: 已授权
00:35:11.389/D: 存储权限开关状态: 关闭
00:35:11.390/D: 5. 检查通知访问权限状态...
00:35:11.391/D: 开始检查通知访问权限状态并同步开关...
00:35:11.391/D: 开始检查通知访问权限状态...
00:35:11.392/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:11.392/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:11.393/D: ✅ 通知访问权限检查：已授权
00:35:11.394/D: 系统权限状态: 已授权
00:35:11.395/D: 功能启用状态: 已启用
00:35:11.396/D: 最终开关状态: 开启
00:35:11.397/D: 通知访问权限状态同步完成
00:35:11.398/D: 6. 检查截图权限状态...
00:35:11.399/D: 开始检查截图权限状态...
00:35:11.399/D: 截图权限检查：使用安全检查方式
00:35:11.400/D: 截图权限检查：requestScreenCapture函数可用
00:35:11.401/D: 截图权限状态: 未授权
00:35:11.402/D: 7. 检查控制台显示状态...
00:35:11.402/D: 开始检查控制台显示状态...
00:35:11.403/D: 控制台显示状态: 已隐藏
00:35:11.404/D: 权限状态显示更新完成（Android 9.0优化）
00:35:11.405/D: 通用抽屉逻辑初始化完成
00:35:11.405/D: 日志页抽屉逻辑初始化完成
00:35:11.410/D: 异步日志文件监听已启动
00:35:11.410/D: 日志逻辑初始化完成
00:35:11.411/D: 初始化通用抽屉逻辑... 主页
00:35:11.412/D: 菜单按钮事件绑定成功
00:35:11.412/D: 遮罩点击事件绑定成功
00:35:11.415/D: 读取权限开关事件绑定成功
00:35:11.418/D: 悬浮窗权限开关事件绑定成功
00:35:11.419/D: 前台服务权限开关事件绑定成功
00:35:11.421/D: 检查通知访问权限开关控件: 找到
00:35:11.422/D: 开始绑定通知访问权限开关事件...
00:35:11.423/D: 通知访问权限开关事件绑定成功
00:35:11.424/D: 检查截图权限开关控件: 找到
00:35:11.424/D: 开始绑定截图权限开关事件...
00:35:11.425/D: 截图权限开关事件绑定成功
00:35:11.426/D: 检查无障碍权限开关控件: 找到
00:35:11.427/D: 开始绑定无障碍权限开关事件...
00:35:11.428/D: 无障碍服务开关事件绑定成功
00:35:11.429/D: 检查控制台显示开关控件: 找到
00:35:11.432/D: 开始绑定控制台显示开关事件...
00:35:11.432/D: 控制台显示开关事件绑定成功
00:35:11.433/D: 权限开关事件绑定完成
00:35:11.434/D: 导航项事件绑定完成
00:35:11.434/D: 开始恢复权限状态...
00:35:11.438/D: 从配置文件读取的权限状态: {
  "无障碍服务": false,
  "悬浮窗权限": true,
  "截图权限": false,
  "前台服务权限": false,
  "读取权限": false,
  "通知访问权限": true,
  "控制台显示": false
}
00:35:11.439/D: 恢复权限状态: 无障碍服务 = false
00:35:11.440/D: 恢复权限状态: 悬浮窗权限 = true
00:35:11.441/D: 恢复权限状态: 截图权限 = false
00:35:11.443/D: 恢复权限状态: 前台服务权限 = false
00:35:11.443/D: 恢复权限状态: 读取权限 = false
00:35:11.444/D: 恢复权限状态: 通知访问权限 = true
00:35:11.444/D: 恢复权限状态: 控制台显示 = false
00:35:11.445/D: 开始恢复UI开关状态...
00:35:11.445/D: 恢复无障碍权限开关: false
00:35:11.446/D: 恢复悬浮窗权限开关: true
00:35:11.447/D: 恢复截图权限开关: false
00:35:11.448/D: 恢复前台服务权限开关: false
00:35:11.449/D: 恢复读取权限开关: false
00:35:11.449/D: 恢复通知访问权限开关: true
00:35:11.450/D: 恢复控制台显示开关: false
00:35:11.450/D: UI开关状态恢复完成
00:35:11.451/D: 开始恢复功能状态...
00:35:11.451/D: 权限状态恢复完成
00:35:11.452/D: 开始更新权限状态显示（Android 9.0优化）...
00:35:11.452/D: 开始检查无障碍服务权限...
00:35:11.453/D: auto对象: function
00:35:11.458/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:11.460/D: 无障碍服务权限检查结果: 已授权
00:35:11.460/D: 无障碍服务权限状态: 已授权
00:35:11.461/D: 2. 检查悬浮球权限状态...
00:35:11.463/D: 3. 检查前台服务权限状态...
00:35:11.463/D: 4. 初始化存储权限状态...
00:35:11.472/D: 存储权限检查：已授权
00:35:11.473/D: 存储权限系统状态: 已授权
00:35:11.474/D: 存储权限开关状态: 关闭
00:35:11.475/D: 5. 检查通知访问权限状态...
00:35:11.476/D: 开始检查通知访问权限状态并同步开关...
00:35:11.477/D: 开始检查通知访问权限状态...
00:35:11.478/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:11.479/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:11.480/D: ✅ 通知访问权限检查：已授权
00:35:11.481/D: 系统权限状态: 已授权
00:35:11.482/D: 功能启用状态: 已启用
00:35:11.482/D: 最终开关状态: 开启
00:35:11.483/D: 通知访问权限状态同步完成
00:35:11.484/D: 6. 检查截图权限状态...
00:35:11.485/D: 开始检查截图权限状态...
00:35:11.485/D: 截图权限检查：使用安全检查方式
00:35:11.486/D: 截图权限检查：requestScreenCapture函数可用
00:35:11.486/D: 截图权限状态: 未授权
00:35:11.487/D: 7. 检查控制台显示状态...
00:35:11.488/D: 开始检查控制台显示状态...
00:35:11.489/D: 控制台显示状态: 已隐藏
00:35:11.490/D: 权限状态显示更新完成（Android 9.0优化）
00:35:11.491/D: 通用抽屉逻辑初始化完成
00:35:11.493/D: 4. 设置全局错误处理...
00:35:11.493/D: 全局错误处理设置完成
00:35:11.494/D: 5. 检查权限状态...
00:35:11.495/D: 基础权限检查完成
00:35:11.496/D: [成功] 基础权限检查完成
00:35:11.496/D: ✅ [12:35:11 上午 GMT+08:00][系统] [系统] 基础权限检查完成
00:35:11.508/D: 6. 加载用户配置...
00:35:11.511/D: ==================================================
00:35:11.513/D: Magic 游戏辅助脚本启动完成！
00:35:11.514/D: 启动时间: 八月 5, 2025 12:35:10 上午 GMT+08:00
00:35:11.515/D: ==================================================
00:35:11.516/D: [成功] 应用启动成功
00:35:11.517/D: ✅ [12:35:11 上午 GMT+08:00][系统] [系统] 应用启动成功
00:35:12.197/D: 自动恢复悬浮窗功能...
00:35:12.199/D: 悬浮球已经在运行中
00:35:12.200/D: 悬浮球启动成功
00:35:12.200/D: ✅ 悬浮窗功能已自动恢复
00:35:12.201/D: 检查通知访问权限功能...
00:35:12.201/D: 开始检查通知访问权限状态...
00:35:12.202/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:12.202/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:12.203/D: ✅ 通知访问权限检查：已授权
00:35:12.203/D: 启用通知访问功能...
00:35:12.204/D: 开始检查通知访问权限状态...
00:35:12.204/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:12.205/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:12.205/D: ✅ 通知访问权限检查：已授权
00:35:12.206/D: ✅ 通知访问功能已启用
00:35:12.207/D: ✅ 通知访问权限功能已自动恢复
00:35:12.207/D: 功能状态恢复完成
00:35:12.324/D: 悬浮球已经在运行中
00:35:12.325/D: 悬浮球自动启动成功
00:35:12.365/D: 自动恢复悬浮窗功能...
00:35:12.368/D: 悬浮球已经在运行中
00:35:12.368/D: 悬浮球启动成功
00:35:12.369/D: ✅ 悬浮窗功能已自动恢复
00:35:12.370/D: 检查通知访问权限功能...
00:35:12.370/D: 开始检查通知访问权限状态...
00:35:12.371/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:12.372/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:12.373/D: ✅ 通知访问权限检查：已授权
00:35:12.373/D: 启用通知访问功能...
00:35:12.374/D: 开始检查通知访问权限状态...
00:35:12.375/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:12.375/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:12.378/D: ✅ 通知访问权限检查：已授权
00:35:12.378/D: ✅ 通知访问功能已启用
00:35:12.379/D: ✅ 通知访问权限功能已自动恢复
00:35:12.379/D: 功能状态恢复完成
00:35:12.466/D: 自动恢复悬浮窗功能...
00:35:12.468/D: 悬浮球已经在运行中
00:35:12.469/D: 悬浮球启动成功
00:35:12.470/D: ✅ 悬浮窗功能已自动恢复
00:35:12.470/D: 检查通知访问权限功能...
00:35:12.472/D: 开始检查通知访问权限状态...
00:35:12.474/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:12.474/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:12.475/D: ✅ 通知访问权限检查：已授权
00:35:12.475/D: 启用通知访问功能...
00:35:12.476/D: 开始检查通知访问权限状态...
00:35:12.476/D: 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:35:12.477/D: 启用的监听器列表: org.autojs.autoxjs.ozobi.v6/com.stardust.notification.NotificationListenerService
00:35:12.478/D: ✅ 通知访问权限检查：已授权
00:35:12.478/D: ✅ 通知访问功能已启用
00:35:12.479/D: ✅ 通知访问权限功能已自动恢复
00:35:12.479/D: 功能状态恢复完成
00:35:21.999/D: 菜单按钮被点击，当前页面: 主页
00:35:22.001/D: 抽屉已打开
00:35:22.001/D: 从日志页面打开抽屉
00:35:22.010/D: 抽屉已打开
00:35:22.010/D: 抽屉页已打开
00:35:22.011/D: 菜单按钮被点击，当前页面: 主页
00:35:22.012/D: 抽屉已关闭
00:35:22.013/D: 菜单按钮被点击，当前页面: 主页
00:35:22.015/D: 抽屉已打开
00:35:23.223/D: 无障碍权限开关被点击，状态: 开启
00:35:23.224/D: 开始检查无障碍服务权限...
00:35:23.224/D: auto对象: function
00:35:23.226/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:23.226/D: 无障碍服务权限检查结果: 已授权
00:35:23.227/D: 开始检查无障碍服务权限...
00:35:23.228/D: auto对象: function
00:35:23.229/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:23.230/D: 无障碍服务权限检查结果: 已授权
00:35:23.231/D: 无障碍服务功能已启用
00:35:23.232/D: 无障碍服务自动化功能现已可用
00:35:23.234/D: 配置已保存: 权限状态.无障碍服务 = true
00:35:23.235/D: 无障碍服务已开启
00:35:23.238/D: 无障碍权限开关被点击，状态: 开启
00:35:23.239/D: 开始检查无障碍服务权限...
00:35:23.239/D: auto对象: function
00:35:23.242/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:23.242/D: 无障碍服务权限检查结果: 已授权
00:35:23.244/D: 开始检查无障碍服务权限...
00:35:23.245/D: auto对象: function
00:35:23.247/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:23.248/D: 无障碍服务权限检查结果: 已授权
00:35:23.248/D: 无障碍服务功能已启用
00:35:23.249/D: 无障碍服务自动化功能现已可用
00:35:23.251/D: 配置已保存: 权限状态.无障碍服务 = true
00:35:23.252/D: 无障碍服务已开启
00:35:23.254/D: 无障碍权限开关被点击，状态: 开启
00:35:23.255/D: 开始检查无障碍服务权限...
00:35:23.255/D: auto对象: function
00:35:23.264/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:23.265/D: 无障碍服务权限检查结果: 已授权
00:35:23.265/D: 开始检查无障碍服务权限...
00:35:23.266/D: auto对象: function
00:35:23.269/D: auto.service: com.google.android.accessibility.selecttospeak.SelectToSpeakService@c46f80c
00:35:23.270/D: 无障碍服务权限检查结果: 已授权
00:35:23.270/D: 无障碍服务功能已启用
00:35:23.272/D: 无障碍服务自动化功能现已可用
00:35:23.276/D: 配置已保存: 权限状态.无障碍服务 = true
00:35:23.277/D: 无障碍服务已开启
00:35:23.975/D: 配置已保存: 权限状态.前台服务权限 = true
00:35:23.977/D: 前台服务权限: 已开启
00:35:23.979/D: 启动前台服务...
00:35:23.995/D: 前台服务权限检查: true 通知权限: true
00:35:24.013/D: 前台服务启动成功
00:35:24.017/D: 配置已保存: 权限状态.前台服务权限 = true
00:35:24.018/D: 前台服务权限: 已开启
00:35:24.018/D: 启动前台服务...
00:35:24.035/D: 前台服务权限检查: true 通知权限: true
00:35:24.038/D: 前台服务启动成功
00:35:24.040/D: 配置已保存: 权限状态.前台服务权限 = true
00:35:24.042/D: 前台服务权限: 已开启
00:35:24.043/D: 启动前台服务...
00:35:24.066/D: 前台服务权限检查: true 通知权限: true
00:35:24.071/D: 前台服务启动成功
00:35:24.396/D: 存储权限检查：已授权
00:35:24.399/D: 配置已保存: 权限状态.读取权限 = true
00:35:24.401/D: 开始验证存储权限功能...
00:35:24.410/D: 配置已保存: 存储权限测试.功能验证 = 存储权限功能正常_1754325324407
00:35:24.419/D: ✅ 配置读写功能验证成功
00:35:24.424/D: 游戏数据已保存: 存储权限测试_数据
00:35:24.426/D: ✅ 游戏数据存储功能验证成功
00:35:24.437/D: 存储权限检查：已授权
00:35:24.440/D: 配置已保存: 权限状态.读取权限 = true
00:35:24.441/D: 开始验证存储权限功能...
00:35:24.451/D: 配置已保存: 存储权限测试.功能验证 = 存储权限功能正常_1754325324449
00:35:24.452/D: ✅ 配置读写功能验证成功
00:35:24.454/D: 游戏数据已保存: 存储权限测试_数据
00:35:24.455/D: ✅ 游戏数据存储功能验证成功
00:35:24.467/D: 存储权限检查：已授权
00:35:24.470/D: 配置已保存: 权限状态.读取权限 = true
00:35:24.471/D: 开始验证存储权限功能...
00:35:24.481/D: 配置已保存: 存储权限测试.功能验证 = 存储权限功能正常_1754325324480
00:35:24.482/D: ✅ 配置读写功能验证成功
00:35:24.485/D: 游戏数据已保存: 存储权限测试_数据
00:35:24.486/D: ✅ 游戏数据存储功能验证成功
00:35:25.012/D: 截图权限开关被点击，状态: 开启
00:35:25.013/D: 用户要求开启截图权限...
00:35:25.014/D: 截图权限检查：使用安全检查方式
00:35:25.015/D: 截图权限检查：requestScreenCapture函数可用
00:35:25.015/D: 开始异步申请截图权限...
00:35:25.017/D: 请求截图权限...
00:35:25.018/D: 使用threads.start()异步申请截图权限...
00:35:25.020/D: 截图权限开关被点击，状态: 开启
00:35:25.020/D: 线程中开始申请截图权限...
00:35:25.021/D: 用户要求开启截图权限...
00:35:25.021/D: 截图权限检查：使用安全检查方式
00:35:25.022/D: 截图权限检查：requestScreenCapture函数可用
00:35:25.022/D: 开始异步申请截图权限...
00:35:25.023/D: 请求截图权限...
00:35:25.024/D: 使用threads.start()异步申请截图权限...
00:35:25.025/D: 截图权限开关被点击，状态: 开启
00:35:25.026/D: 用户要求开启截图权限...
00:35:25.026/D: 截图权限检查：使用安全检查方式
00:35:25.025/D: 线程中开始申请截图权限...
00:35:25.027/D: 截图权限检查：requestScreenCapture函数可用
00:35:25.028/D: 开始异步申请截图权限...
00:35:25.029/D: 请求截图权限...
00:35:25.029/D: 使用threads.start()异步申请截图权限...
00:35:25.031/D: 线程中开始申请截图权限...
00:35:27.435/D: 配置已保存: 存储权限测试.功能验证 = undefined
00:35:27.437/E: 保存游戏数据失败: TypeError: value cannot be undefined
00:35:27.437/D: 测试数据已清理
00:35:27.468/D: 配置已保存: 存储权限测试.功能验证 = undefined
00:35:27.469/E: 保存游戏数据失败: TypeError: value cannot be undefined
00:35:27.469/D: 测试数据已清理
00:35:27.501/D: 配置已保存: 存储权限测试.功能验证 = undefined
00:35:27.502/E: 保存游戏数据失败: TypeError: value cannot be undefined
00:35:27.503/D: 测试数据已清理
00:35:29.859/D: 截图权限开关被点击，状态: 开启
00:35:29.860/D: 用户要求开启截图权限...
00:35:29.861/D: 截图权限检查：使用安全检查方式
00:35:29.861/D: 截图权限检查：requestScreenCapture函数可用
00:35:29.862/D: 开始异步申请截图权限...
00:35:29.863/D: 请求截图权限...
00:35:29.864/D: 使用threads.start()异步申请截图权限...
00:35:29.866/D: 截图权限开关被点击，状态: 开启
00:35:29.867/D: 用户要求开启截图权限...
00:35:29.867/D: 线程中开始申请截图权限...
00:35:29.868/D: 截图权限检查：使用安全检查方式
00:35:29.868/D: 截图权限检查：requestScreenCapture函数可用
00:35:29.869/D: 开始异步申请截图权限...
00:35:29.869/D: 请求截图权限...
00:35:29.871/D: 使用threads.start()异步申请截图权限...
00:35:29.872/D: 截图权限开关被点击，状态: 开启
00:35:29.872/D: 线程中开始申请截图权限...
00:35:29.872/D: 用户要求开启截图权限...
00:35:29.874/D: 截图权限检查：使用安全检查方式
00:35:29.874/D: 截图权限检查：requestScreenCapture函数可用
00:35:29.875/D: 开始异步申请截图权限...
00:35:29.876/D: 请求截图权限...
00:35:29.876/D: 使用threads.start()异步申请截图权限...
00:35:29.879/D: 线程中开始申请截图权限...
00:35:30.380/D: 截图权限申请成功
00:35:30.382/D: 截图权限开关被点击，状态: 开启
00:35:30.383/D: 用户要求开启截图权限...
00:35:30.383/D: 截图权限检查：使用安全检查方式
00:35:30.384/D: 截图权限检查：requestScreenCapture函数可用
00:35:30.385/D: 开始异步申请截图权限...
00:35:30.386/D: 请求截图权限...
00:35:30.386/D: 使用threads.start()异步申请截图权限...
00:35:30.388/D: 截图权限开关被点击，状态: 开启
00:35:30.388/D: 用户要求开启截图权限...
00:35:30.389/D: 截图权限检查：使用安全检查方式
00:35:30.389/D: 线程中开始申请截图权限...
00:35:30.390/D: 截图权限检查：requestScreenCapture函数可用
00:35:30.390/D: 开始异步申请截图权限...
00:35:30.392/D: 请求截图权限...
00:35:30.392/D: 使用threads.start()异步申请截图权限...
00:35:30.405/D: 截图权限开关被点击，状态: 开启
00:35:30.408/D: 用户要求开启截图权限...
00:35:30.406/D: 线程中开始申请截图权限...
00:35:30.412/D: 截图权限检查：使用安全检查方式
00:35:30.414/D: 截图权限检查：requestScreenCapture函数可用
00:35:30.415/D: 开始异步申请截图权限...
00:35:30.418/D: 请求截图权限...
00:35:30.419/D: 使用threads.start()异步申请截图权限...
00:35:30.427/D: 线程中开始申请截图权限...
00:35:30.692/D: 截图权限申请成功
00:35:30.693/D: 截图权限开关被点击，状态: 开启
00:35:30.694/D: 用户要求开启截图权限...
00:35:30.695/D: 截图权限检查：使用安全检查方式
00:35:30.695/D: 截图权限检查：内部状态显示已授权
00:35:30.697/D: 配置已保存: 权限状态.截图权限 = true
00:35:30.697/D: 截图权限已开启
00:35:30.698/D: 截图权限开关被点击，状态: 开启
00:35:30.699/D: 用户要求开启截图权限...
00:35:30.701/D: 截图权限检查：使用安全检查方式
00:35:30.702/D: 截图权限检查：内部状态显示已授权
00:35:30.703/D: 配置已保存: 权限状态.截图权限 = true
00:35:30.704/D: 截图权限已开启
00:35:30.705/D: 截图权限开关被点击，状态: 开启
00:35:30.706/D: 用户要求开启截图权限...
00:35:30.709/D: 截图权限检查：使用安全检查方式
00:35:30.713/D: 截图权限检查：内部状态显示已授权
00:35:30.716/D: 配置已保存: 权限状态.截图权限 = true
00:35:30.717/D: 截图权限已开启
00:35:30.718/D: 截图权限申请成功
00:35:30.727/D: 截图权限申请成功
00:35:31.426/D: 截图权限检查：使用安全检查方式
00:35:31.426/D: 截图权限检查：内部状态显示已授权
00:35:31.427/D: ✅ 截图权限验证成功
00:35:31.719/D: 截图权限检查：使用安全检查方式
00:35:31.720/D: 截图权限检查：内部状态显示已授权
00:35:31.720/D: ✅ 截图权限验证成功
00:35:31.721/D: 截图权限检查：使用安全检查方式
00:35:31.723/D: 截图权限检查：内部状态显示已授权
00:35:31.724/D: ✅ 截图权限验证成功
00:35:31.765/D: 截图权限检查：使用安全检查方式
00:35:31.766/D: 截图权限检查：内部状态显示已授权
00:35:31.766/D: ✅ 截图权限验证成功
00:35:32.050/D: 抽屉已关闭
00:35:32.051/D: 抽屉已关闭
00:35:32.053/D: 抽屉已关闭
00:35:35.496/D: 触摸开始: 24.1875 695.1111450195312
00:35:35.581/D: 触摸结束，拖拽状态: false
00:35:35.634/D: 悬浮球被点击（非拖拽）
00:35:35.635/D: 主悬浮球被点击
00:35:35.661/D: 开始展开悬浮球菜单...
00:35:35.703/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:35:35.703/D: 菜单图标应用成功: 开始
00:35:35.704/D: 菜单事件绑定成功: 开始
00:35:35.705/D: 菜单图标创建成功: 开始
00:35:35.742/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:35:35.743/D: 菜单图标应用成功: 停止
00:35:35.743/D: 菜单事件绑定成功: 停止
00:35:35.744/D: 菜单图标创建成功: 停止
00:35:35.778/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:35:35.779/D: 菜单图标应用成功: 主页
00:35:35.780/D: 菜单事件绑定成功: 主页
00:35:35.780/D: 菜单图标创建成功: 主页
00:35:35.815/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:35:35.815/D: 菜单图标应用成功: 日志
00:35:35.816/D: 菜单事件绑定成功: 日志
00:35:35.817/D: 菜单图标创建成功: 日志
00:35:35.817/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:35:36.782/D: 菜单被点击: 开始
00:35:36.797/D: 🚀 悬浮球：准备启动算数程序...
00:35:36.798/D: 🔄 悬浮球：延迟加载算数程序模块...
00:35:37.032/D: ✅ 屏幕捕获权限获取成功
00:35:37.056/D: ✅ 悬浮球：算数程序模块加载成功
00:35:37.057/D: ✅ 悬浮球：算数程序启动请求已发送
00:35:37.058/D: 🎯 悬浮球：开始执行算数程序...
00:35:37.061/D: 🚀 算数程序开始执行
00:35:37.061/D: 🔄 开始执行算数游戏循环，共3局
00:35:37.062/D: ═══════════════════════════════════════
00:35:37.062/D: 🎮 第1局开始 (1/3)
00:35:37.074/D: ───────────────────────────────────────
00:35:37.074/D: 🎯 启动算数游戏 - Brain Battle
00:35:37.078/D: 📋 算数游戏未运行，尝试启动游戏
00:35:37.081/D: 🚀 开始应用登陆流程: Brain Battle
00:35:37.082/D: 📱 当前应用: com.android.launcher3
00:35:37.082/D: 🚀 正在启动应用: Brain Battle
00:35:37.131/D: ⏳ 等待应用启动完成...
00:35:37.359/D: 开始收起悬浮球菜单...
00:35:37.362/D: 悬浮球菜单收起成功
00:35:38.138/D: ⚠️ 应用启动超时: Brain Battle (等待时间: 1000ms)
00:35:38.138/D: ⚠️ 算数游戏启动失败，但继续执行后续功能（可能游戏已在运行）
00:35:38.139/D: 📍 当前步骤: 第2步：帐号登陆
00:35:38.139/D: 📋 步骤2: 检查帐号登陆开关状态
00:35:38.140/D: 🔧 首次帐号注册开关状态: 关闭
00:35:38.141/D: ⏭️ 步骤2: 跳过帐号登陆（开关已关闭）
00:35:38.142/D: 📍 当前步骤: 第3步：算数首次教程
00:35:38.142/D: 📋 步骤3: 检查过游戏教程开关状态
00:35:38.143/D: 🔧 过游戏教程开关状态: 关闭
00:35:38.144/D: ⏭️ 步骤3: 跳过算数首次教程（开关已关闭）
00:35:38.144/D: 📍 当前步骤: 第4步：首次提现教程
00:35:38.145/D: ⏭️ 步骤4: 跳过首次提现教程（开关已关闭）
00:35:38.146/D: ⏳ 延时200毫秒...
00:35:38.348/D: 📋 步骤3.2: 执行每日领奖
00:35:38.349/D: 🎁 每日领奖流程
00:35:38.349/D: 🔐 检查截图权限...
00:35:38.582/D: ✅ 截图权限申请成功
00:35:38.787/D: 📝 第一步：点击bonus按钮
00:35:38.788/D: 🔍 图片路径: ../../assets/算数游戏/新手教程图片/bonus.png
00:35:38.790/D: 🔍 开始判断点击图片: ../../assets/算数游戏/新手教程图片/bonus.png (相似度: 0.8)
00:35:38.791/D: 🎯 在指定区域判断点击图片...
00:35:38.793/D: ❌ 图片读取失败: ../../assets/算数游戏/新手教程图片/bonus.png
00:35:38.798/D: ⚠️ 未找到按钮：可能已经领取过了，结束每日领奖
00:35:38.799/D: 🎉 每日领奖程序结束
00:35:38.800/D: ✅ 每日领奖完成
00:35:38.804/D: ⏳ 随机等待 128 毫秒后开始点击开始界面
00:35:38.945/D: 📋 步骤3.5: 执行点击开始界面
00:35:38.946/D: 🎮 开始界面点击流程
00:35:38.947/D: 🔐 检查截图权限...
00:35:39.159/D: ✅ 截图权限申请成功
00:35:39.261/D: 📝 第一步：点击开始界面
00:35:39.262/D: 🔍 图片路径: ../../assets/算数游戏/新手教程图片/开始界面.png
00:35:39.263/D: 🔍 开始判断点击图片: ../../assets/算数游戏/新手教程图片/开始界面.png (相似度: 0.7)
00:35:39.275/D: 🎯 在指定区域判断点击图片...
00:35:39.276/D: ❌ 图片读取失败: ../../assets/算数游戏/新手教程图片/开始界面.png
00:35:39.277/D: ⚠️ 第一步未找到按钮：可能已经在开始界面了
00:35:39.278/D: ⏳ 随机延时 112 毫秒...
00:35:39.408/D: 📝 第二步：点击开始游戏按钮
00:35:39.408/D: 🔍 图片路径: ../../assets/算数游戏/新手教程图片/开始按钮.png
00:35:39.410/D: 🔍 开始判断点击图片: ../../assets/算数游戏/新手教程图片/开始按钮.png (相似度: 0.7)
00:35:39.429/D: 🎯 在指定区域判断点击图片...
00:35:39.432/D: ❌ 图片读取失败: ../../assets/算数游戏/新手教程图片/开始按钮.png
00:35:39.434/D: ⚠️ 第二步未找到按钮：可能已经开始游戏了
00:35:39.435/D: ✅ 点击开始界面完成
00:35:39.437/D: ⏳ 随机等待 193 毫秒后开始算数游戏识别和计算
00:35:39.637/D: 📋 步骤4: 开始算数游戏识别和计算
00:35:39.638/D: 🧮 启动三区域公式识别引擎
00:35:39.648/I: opencv initializing
00:35:40.201/I: opencv initialized
00:35:40.482/D: 🔍 第一步：检测是否在开始界面...
00:35:40.482/D: 🔍 开始查找图片: ../../assets/算数游戏/新手教程图片/点击分数.png (相似度: 0.9)
00:35:40.485/D: 🎯 在指定区域查找图片...
00:35:40.486/D: ❌ 图片读取失败: ../../assets/算数游戏/新手教程图片/点击分数.png
00:35:40.487/D: ⚠️ 未检测到开始界面，但继续执行表达式识别
00:35:40.488/D: ⏳ 延时100毫秒后开始循环识别...
00:35:40.589/D: 🎯 本次执行循环次数: 107
00:35:40.590/D: 🎯 故意点错次数: 105
00:35:40.591/D: ------------------------截图功能--------------------------
00:35:40.592/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.598/D: ❌ 第1次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.598/D: ------------------------第1次循环完成--------------------------
00:35:40.599/D: ------------------------截图功能--------------------------
00:35:40.599/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.600/D: ❌ 第2次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.601/D: ------------------------第2次循环完成--------------------------
00:35:40.602/D: ------------------------截图功能--------------------------
00:35:40.602/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.603/D: ❌ 第3次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.603/D: ------------------------第3次循环完成--------------------------
00:35:40.604/D: ------------------------截图功能--------------------------
00:35:40.605/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.606/D: ❌ 第4次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.607/D: ------------------------第4次循环完成--------------------------
00:35:40.607/D: ------------------------截图功能--------------------------
00:35:40.608/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.609/D: ❌ 第5次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.610/D: ------------------------第5次循环完成--------------------------
00:35:40.610/D: ------------------------截图功能--------------------------
00:35:40.611/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.611/D: ❌ 第6次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.612/D: ------------------------第6次循环完成--------------------------
00:35:40.612/D: ------------------------截图功能--------------------------
00:35:40.613/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.614/D: ❌ 第7次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.615/D: ------------------------第7次循环完成--------------------------
00:35:40.615/D: ------------------------截图功能--------------------------
00:35:40.616/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.616/D: ❌ 第8次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.617/D: ------------------------第8次循环完成--------------------------
00:35:40.618/D: ------------------------截图功能--------------------------
00:35:40.618/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.619/D: ❌ 第9次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.620/D: ------------------------第9次循环完成--------------------------
00:35:40.621/D: ------------------------截图功能--------------------------
00:35:40.621/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.625/D: ❌ 第10次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.627/D: ------------------------第10次循环完成--------------------------
00:35:40.628/D: ------------------------截图功能--------------------------
00:35:40.630/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.635/D: ❌ 第11次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.637/D: ------------------------第11次循环完成--------------------------
00:35:40.639/D: ------------------------截图功能--------------------------
00:35:40.639/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.642/D: ❌ 第12次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.643/D: ------------------------第12次循环完成--------------------------
00:35:40.646/D: ------------------------截图功能--------------------------
00:35:40.646/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.648/D: ❌ 第13次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.650/D: ------------------------第13次循环完成--------------------------
00:35:40.651/D: ------------------------截图功能--------------------------
00:35:40.651/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.652/D: ❌ 第14次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.653/D: ------------------------第14次循环完成--------------------------
00:35:40.654/D: ------------------------截图功能--------------------------
00:35:40.655/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.658/D: ❌ 第15次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.660/D: ------------------------第15次循环完成--------------------------
00:35:40.661/D: ------------------------截图功能--------------------------
00:35:40.662/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.664/D: ❌ 第16次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.666/D: ------------------------第16次循环完成--------------------------
00:35:40.668/D: ------------------------截图功能--------------------------
00:35:40.669/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.670/D: ❌ 第17次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.671/D: ------------------------第17次循环完成--------------------------
00:35:40.673/D: ------------------------截图功能--------------------------
00:35:40.673/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.674/D: ❌ 第18次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.676/D: ------------------------第18次循环完成--------------------------
00:35:40.678/D: ------------------------截图功能--------------------------
00:35:40.678/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.680/D: ❌ 第19次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.681/D: ------------------------第19次循环完成--------------------------
00:35:40.682/D: ------------------------截图功能--------------------------
00:35:40.683/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.684/D: ❌ 第20次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.685/D: ------------------------第20次循环完成--------------------------
00:35:40.686/D: ------------------------截图功能--------------------------
00:35:40.686/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.687/D: ❌ 第21次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.688/D: ------------------------第21次循环完成--------------------------
00:35:40.689/D: ------------------------截图功能--------------------------
00:35:40.690/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.697/D: ❌ 第22次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.698/D: ------------------------第22次循环完成--------------------------
00:35:40.699/D: ------------------------截图功能--------------------------
00:35:40.700/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.702/D: ❌ 第23次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.703/D: ------------------------第23次循环完成--------------------------
00:35:40.704/D: ------------------------截图功能--------------------------
00:35:40.705/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.707/D: ❌ 第24次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.708/D: ------------------------第24次循环完成--------------------------
00:35:40.709/D: ------------------------截图功能--------------------------
00:35:40.710/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.712/D: ❌ 第25次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.713/D: ------------------------第25次循环完成--------------------------
00:35:40.714/D: ------------------------截图功能--------------------------
00:35:40.715/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.717/D: ❌ 第26次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.718/D: ------------------------第26次循环完成--------------------------
00:35:40.719/D: ------------------------截图功能--------------------------
00:35:40.719/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.721/D: ❌ 第27次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.722/D: ------------------------第27次循环完成--------------------------
00:35:40.723/D: ------------------------截图功能--------------------------
00:35:40.725/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.728/D: ❌ 第28次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.731/D: ------------------------第28次循环完成--------------------------
00:35:40.733/D: ------------------------截图功能--------------------------
00:35:40.733/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.736/D: ❌ 第29次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.738/D: ------------------------第29次循环完成--------------------------
00:35:40.739/D: ------------------------截图功能--------------------------
00:35:40.740/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.744/D: ❌ 第30次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.746/D: ------------------------第30次循环完成--------------------------
00:35:40.747/D: ------------------------截图功能--------------------------
00:35:40.748/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.748/D: ❌ 第31次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.749/D: ------------------------第31次循环完成--------------------------
00:35:40.750/D: ------------------------截图功能--------------------------
00:35:40.750/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.751/D: ❌ 第32次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.752/D: ------------------------第32次循环完成--------------------------
00:35:40.753/D: ------------------------截图功能--------------------------
00:35:40.753/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.754/D: ❌ 第33次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.754/D: ------------------------第33次循环完成--------------------------
00:35:40.755/D: ------------------------截图功能--------------------------
00:35:40.756/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.757/D: ❌ 第34次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.757/D: ------------------------第34次循环完成--------------------------
00:35:40.760/D: ------------------------截图功能--------------------------
00:35:40.761/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.762/D: ❌ 第35次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.763/D: ------------------------第35次循环完成--------------------------
00:35:40.764/D: ------------------------截图功能--------------------------
00:35:40.764/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.765/D: ❌ 第36次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.766/D: ------------------------第36次循环完成--------------------------
00:35:40.766/D: ------------------------截图功能--------------------------
00:35:40.767/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.768/D: ❌ 第37次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.768/D: ------------------------第37次循环完成--------------------------
00:35:40.769/D: ------------------------截图功能--------------------------
00:35:40.770/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.771/D: ❌ 第38次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.771/D: ------------------------第38次循环完成--------------------------
00:35:40.772/D: ------------------------截图功能--------------------------
00:35:40.772/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.773/D: ❌ 第39次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.774/D: ------------------------第39次循环完成--------------------------
00:35:40.775/D: ------------------------截图功能--------------------------
00:35:40.775/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.777/D: ❌ 第40次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.779/D: ------------------------第40次循环完成--------------------------
00:35:40.780/D: ------------------------截图功能--------------------------
00:35:40.781/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.782/D: ❌ 第41次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.783/D: ------------------------第41次循环完成--------------------------
00:35:40.785/D: ------------------------截图功能--------------------------
00:35:40.785/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.789/D: ❌ 第42次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.790/D: ------------------------第42次循环完成--------------------------
00:35:40.792/D: ------------------------截图功能--------------------------
00:35:40.792/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.794/D: ❌ 第43次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.795/D: ------------------------第43次循环完成--------------------------
00:35:40.795/D: ------------------------截图功能--------------------------
00:35:40.796/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.797/D: ❌ 第44次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.797/D: ------------------------第44次循环完成--------------------------
00:35:40.799/D: ------------------------截图功能--------------------------
00:35:40.799/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.800/D: ❌ 第45次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.801/D: ------------------------第45次循环完成--------------------------
00:35:40.802/D: ------------------------截图功能--------------------------
00:35:40.804/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.806/D: ❌ 第46次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.807/D: ------------------------第46次循环完成--------------------------
00:35:40.808/D: ------------------------截图功能--------------------------
00:35:40.809/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.810/D: ❌ 第47次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.811/D: ------------------------第47次循环完成--------------------------
00:35:40.812/D: ------------------------截图功能--------------------------
00:35:40.812/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.813/D: ❌ 第48次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.814/D: ------------------------第48次循环完成--------------------------
00:35:40.815/D: ------------------------截图功能--------------------------
00:35:40.815/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.817/D: ❌ 第49次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.817/D: ------------------------第49次循环完成--------------------------
00:35:40.818/D: ------------------------截图功能--------------------------
00:35:40.818/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.819/D: ❌ 第50次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.820/D: ------------------------第50次循环完成--------------------------
00:35:40.820/D: ------------------------截图功能--------------------------
00:35:40.822/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.822/D: ❌ 第51次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.823/D: ------------------------第51次循环完成--------------------------
00:35:40.823/D: ------------------------截图功能--------------------------
00:35:40.824/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.825/D: ❌ 第52次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.825/D: ------------------------第52次循环完成--------------------------
00:35:40.826/D: ------------------------截图功能--------------------------
00:35:40.827/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.828/D: ❌ 第53次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.829/D: ------------------------第53次循环完成--------------------------
00:35:40.829/D: ------------------------截图功能--------------------------
00:35:40.830/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.831/D: ❌ 第54次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.831/D: ------------------------第54次循环完成--------------------------
00:35:40.832/D: ------------------------截图功能--------------------------
00:35:40.832/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.834/D: ❌ 第55次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.836/D: ------------------------第55次循环完成--------------------------
00:35:40.836/D: ------------------------截图功能--------------------------
00:35:40.837/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.838/D: ❌ 第56次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.840/D: ------------------------第56次循环完成--------------------------
00:35:40.840/D: ------------------------截图功能--------------------------
00:35:40.847/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.849/D: ❌ 第57次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.851/D: ------------------------第57次循环完成--------------------------
00:35:40.852/D: ------------------------截图功能--------------------------
00:35:40.853/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.854/D: ❌ 第58次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.855/D: ------------------------第58次循环完成--------------------------
00:35:40.856/D: ------------------------截图功能--------------------------
00:35:40.857/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.860/D: ❌ 第59次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.861/D: ------------------------第59次循环完成--------------------------
00:35:40.863/D: ------------------------截图功能--------------------------
00:35:40.864/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.866/D: ❌ 第60次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.868/D: ------------------------第60次循环完成--------------------------
00:35:40.868/D: ------------------------截图功能--------------------------
00:35:40.870/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.871/D: ❌ 第61次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.872/D: ------------------------第61次循环完成--------------------------
00:35:40.872/D: ------------------------截图功能--------------------------
00:35:40.873/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.874/D: ❌ 第62次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.879/D: ------------------------第62次循环完成--------------------------
00:35:40.880/D: ------------------------截图功能--------------------------
00:35:40.881/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.883/D: ❌ 第63次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.884/D: ------------------------第63次循环完成--------------------------
00:35:40.885/D: ------------------------截图功能--------------------------
00:35:40.885/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.887/D: ❌ 第64次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.888/D: ------------------------第64次循环完成--------------------------
00:35:40.888/D: ------------------------截图功能--------------------------
00:35:40.889/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.889/D: ❌ 第65次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.890/D: ------------------------第65次循环完成--------------------------
00:35:40.894/D: ------------------------截图功能--------------------------
00:35:40.894/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.895/D: ❌ 第66次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.896/D: ------------------------第66次循环完成--------------------------
00:35:40.896/D: ------------------------截图功能--------------------------
00:35:40.897/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.899/D: ❌ 第67次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.900/D: ------------------------第67次循环完成--------------------------
00:35:40.900/D: ------------------------截图功能--------------------------
00:35:40.901/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.901/D: ❌ 第68次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.902/D: ------------------------第68次循环完成--------------------------
00:35:40.902/D: ------------------------截图功能--------------------------
00:35:40.903/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.904/D: ❌ 第69次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.905/D: ------------------------第69次循环完成--------------------------
00:35:40.906/D: ------------------------截图功能--------------------------
00:35:40.906/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.908/D: ❌ 第70次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.908/D: ------------------------第70次循环完成--------------------------
00:35:40.909/D: ------------------------截图功能--------------------------
00:35:40.911/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.912/D: ❌ 第71次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.913/D: ------------------------第71次循环完成--------------------------
00:35:40.913/D: ------------------------截图功能--------------------------
00:35:40.914/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.914/D: ❌ 第72次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.915/D: ------------------------第72次循环完成--------------------------
00:35:40.916/D: ------------------------截图功能--------------------------
00:35:40.916/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.917/D: ❌ 第73次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.918/D: ------------------------第73次循环完成--------------------------
00:35:40.919/D: ------------------------截图功能--------------------------
00:35:40.919/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.920/D: ❌ 第74次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.920/D: ------------------------第74次循环完成--------------------------
00:35:40.922/D: ------------------------截图功能--------------------------
00:35:40.923/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.923/D: ❌ 第75次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.924/D: ------------------------第75次循环完成--------------------------
00:35:40.924/D: ------------------------截图功能--------------------------
00:35:40.925/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.926/D: ❌ 第76次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.927/D: ------------------------第76次循环完成--------------------------
00:35:40.928/D: ------------------------截图功能--------------------------
00:35:40.928/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.929/D: ❌ 第77次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.930/D: ------------------------第77次循环完成--------------------------
00:35:40.930/D: ------------------------截图功能--------------------------
00:35:40.931/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.932/D: ❌ 第78次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.932/D: ------------------------第78次循环完成--------------------------
00:35:40.933/D: ------------------------截图功能--------------------------
00:35:40.938/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.939/D: ❌ 第79次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.939/D: ------------------------第79次循环完成--------------------------
00:35:40.940/D: ------------------------截图功能--------------------------
00:35:40.941/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.942/D: ❌ 第80次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.943/D: ------------------------第80次循环完成--------------------------
00:35:40.944/D: ------------------------截图功能--------------------------
00:35:40.945/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.946/D: ❌ 第81次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.947/D: ------------------------第81次循环完成--------------------------
00:35:40.948/D: ------------------------截图功能--------------------------
00:35:40.949/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.950/D: ❌ 第82次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.951/D: ------------------------第82次循环完成--------------------------
00:35:40.951/D: ------------------------截图功能--------------------------
00:35:40.952/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.953/D: ❌ 第83次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.954/D: ------------------------第83次循环完成--------------------------
00:35:40.955/D: ------------------------截图功能--------------------------
00:35:40.956/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.963/D: ❌ 第84次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.964/D: ------------------------第84次循环完成--------------------------
00:35:40.967/D: ------------------------截图功能--------------------------
00:35:40.968/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.970/D: ❌ 第85次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.971/D: ------------------------第85次循环完成--------------------------
00:35:40.971/D: ------------------------截图功能--------------------------
00:35:40.972/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.973/D: ❌ 第86次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.975/D: ------------------------第86次循环完成--------------------------
00:35:40.976/D: ------------------------截图功能--------------------------
00:35:40.976/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.978/D: ❌ 第87次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.979/D: ------------------------第87次循环完成--------------------------
00:35:40.979/D: ------------------------截图功能--------------------------
00:35:40.981/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.984/D: ❌ 第88次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.984/D: ------------------------第88次循环完成--------------------------
00:35:40.985/D: ------------------------截图功能--------------------------
00:35:40.986/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.986/D: ❌ 第89次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.987/D: ------------------------第89次循环完成--------------------------
00:35:40.988/D: ------------------------截图功能--------------------------
00:35:40.988/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.990/D: ❌ 第90次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.991/D: ------------------------第90次循环完成--------------------------
00:35:40.992/D: ------------------------截图功能--------------------------
00:35:40.993/D: ✅ 全屏截图成功，开始三区域识别
00:35:40.995/D: ❌ 第91次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:40.997/D: ------------------------第91次循环完成--------------------------
00:35:40.998/D: ------------------------截图功能--------------------------
00:35:40.999/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.003/D: ❌ 第92次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.004/D: ------------------------第92次循环完成--------------------------
00:35:41.006/D: ------------------------截图功能--------------------------
00:35:41.007/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.008/D: ❌ 第93次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.009/D: ------------------------第93次循环完成--------------------------
00:35:41.011/D: ------------------------截图功能--------------------------
00:35:41.011/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.013/D: ❌ 第94次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.013/D: ------------------------第94次循环完成--------------------------
00:35:41.016/D: ------------------------截图功能--------------------------
00:35:41.017/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.018/D: ❌ 第95次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.020/D: ------------------------第95次循环完成--------------------------
00:35:41.021/D: ------------------------截图功能--------------------------
00:35:41.022/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.023/D: ❌ 第96次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.025/D: ------------------------第96次循环完成--------------------------
00:35:41.026/D: ------------------------截图功能--------------------------
00:35:41.028/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.035/D: ❌ 第97次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.039/D: ------------------------第97次循环完成--------------------------
00:35:41.043/D: ------------------------截图功能--------------------------
00:35:41.044/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.046/D: ❌ 第98次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.049/D: ------------------------第98次循环完成--------------------------
00:35:41.052/D: ------------------------截图功能--------------------------
00:35:41.053/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.055/D: ❌ 第99次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.056/D: ------------------------第99次循环完成--------------------------
00:35:41.059/D: ------------------------截图功能--------------------------
00:35:41.060/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.062/D: ❌ 第100次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.065/D: ------------------------第100次循环完成--------------------------
00:35:41.067/D: ------------------------截图功能--------------------------
00:35:41.068/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.069/D: ❌ 第101次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.070/D: ------------------------第101次循环完成--------------------------
00:35:41.071/D: ------------------------截图功能--------------------------
00:35:41.072/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.073/D: ❌ 第102次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.074/D: ------------------------第102次循环完成--------------------------
00:35:41.075/D: ------------------------截图功能--------------------------
00:35:41.077/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.079/D: ❌ 第103次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.080/D: ------------------------第103次循环完成--------------------------
00:35:41.081/D: ------------------------截图功能--------------------------
00:35:41.082/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.083/D: ❌ 第104次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.083/D: ------------------------第104次循环完成--------------------------
00:35:41.084/D: ------------------------截图功能--------------------------
00:35:41.085/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.086/D: ❌ 第105次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.086/D: ------------------------第105次循环完成--------------------------
00:35:41.087/D: ------------------------截图功能--------------------------
00:35:41.088/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.088/D: ❌ 第106次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.089/D: ------------------------第106次循环完成--------------------------
00:35:41.091/D: ------------------------截图功能--------------------------
00:35:41.093/D: ✅ 全屏截图成功，开始三区域识别
00:35:41.095/D: ❌ 第107次循环执行失败: JavaException: java.lang.IllegalStateException: image has been recycled
00:35:41.095/D: ------------------------第107次循环完成--------------------------
00:35:41.096/D: -------------V：1.9.13-----------执行完成--------------------------
00:35:41.097/D: 🎬 开始执行看广告程序...
00:35:41.129/D: ❌ 看广告模块加载失败
00:35:41.130/D: ✅ 算数游戏识别引擎执行完成
00:35:41.132/D: ⏳ 随机等待 9931 毫秒后开始看广告功能
00:35:48.984/D: 触摸开始: 11.25 684.4444580078125
00:35:49.112/D: 触摸结束，拖拽状态: false
00:35:49.221/D: 悬浮球被点击（非拖拽）
00:35:49.222/D: 主悬浮球被点击
00:35:49.318/D: 开始展开悬浮球菜单...
00:35:49.774/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:35:49.775/D: 菜单图标应用成功: 开始
00:35:49.775/D: 菜单事件绑定成功: 开始
00:35:49.776/D: 菜单图标创建成功: 开始
00:35:49.851/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:35:49.853/D: 菜单图标应用成功: 停止
00:35:49.859/D: 菜单事件绑定成功: 停止
00:35:49.860/D: 菜单图标创建成功: 停止
00:35:49.955/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:35:49.957/D: 菜单图标应用成功: 主页
00:35:49.983/D: 菜单事件绑定成功: 主页
00:35:49.985/D: 菜单图标创建成功: 主页
00:35:50.069/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:35:50.071/D: 菜单图标应用成功: 日志
00:35:50.073/D: 菜单事件绑定成功: 日志
00:35:50.077/D: 菜单图标创建成功: 日志
00:35:50.078/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:35:51.115/D: 
=== 看广告精准符号识别功能开始 ===
00:35:51.117/D: 🔄 开始第1次看广告循环...
00:35:51.118/D: 🔍 检查是否返回游戏开始界面...
00:35:51.327/D: ⚠️ 无法读取游戏开始界面图片
00:35:51.338/D: ⏰ 等待3秒后开始识别...
00:35:52.582/D: 菜单被点击: 停止
00:35:52.655/D: 🛑 悬浮球：准备停止算数程序...
00:35:52.656/D: 🛑 收到停止脚本请求
00:35:52.672/D: ✅ 悬浮球：算数程序停止请求已发送
00:35:53.310/D: 开始收起悬浮球菜单...
00:35:53.325/D: 悬浮球菜单收起成功
00:35:54.339/D: 📸 开始截图识别
00:35:54.365/D: 🔍 检查是否在游戏开始界面...
00:35:54.370/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:35:54.376/D: 🚀 启动统一识别符号引擎...
00:35:54.749/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:35:55.411/D: 触摸开始: 16.875 680.888916015625
00:35:55.446/D: 触摸结束，拖拽状态: false
00:35:55.501/D: 悬浮球被点击（非拖拽）
00:35:55.506/D: 主悬浮球被点击
00:35:55.562/D: 开始展开悬浮球菜单...
00:35:56.397/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:35:56.400/D: 菜单图标应用成功: 开始
00:35:56.402/D: 菜单事件绑定成功: 开始
00:35:56.412/D: 菜单图标创建成功: 开始
00:35:56.618/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:35:56.623/D: 菜单图标应用成功: 停止
00:35:56.627/D: 菜单事件绑定成功: 停止
00:35:56.629/D: 菜单图标创建成功: 停止
00:35:56.821/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:35:56.825/D: 菜单图标应用成功: 主页
00:35:56.830/D: 菜单事件绑定成功: 主页
00:35:56.833/D: 菜单图标创建成功: 主页
00:35:56.993/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:35:56.997/D: 菜单图标应用成功: 日志
00:35:57.002/D: 菜单事件绑定成功: 日志
00:35:57.008/D: 菜单图标创建成功: 日志
00:35:57.015/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:36:03.442/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:36:03.956/D: 菜单被点击: 停止
00:36:04.119/D: 🛑 悬浮球：准备停止算数程序...
00:36:04.123/D: 🛑 收到停止脚本请求
00:36:04.126/D: ✅ 悬浮球：算数程序停止请求已发送
00:36:04.780/D: 开始收起悬浮球菜单...
00:36:04.781/D: 悬浮球菜单收起成功
00:36:06.187/D:    🏆 总耗时: 11804ms，最终识别到: 4 个符号
00:36:06.207/D: 
📋 符号识别结果:
00:36:06.209/D:   🔣 识别到符号: 4 个
00:36:06.212/D: 🔣 符号详情: V×Login(454,56) 次(299,83) 停止请求已发送(284,887) 停止请求已发送(273,887)
00:36:06.216/D: ❌ 未找到可点击的符号
00:36:06.217/D: 🎯 第八步：检查游戏状态...
00:36:06.223/D: 🔍 当前应用包名: com.winrgames.brainbattle
00:36:06.224/D: ✅ 算数游戏已在前台
00:36:06.226/D: ⏳ 等待2秒，让界面稳定...
00:36:06.328/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:36:08.228/D: ✅ 等待完成，游戏状态检查完毕
00:36:08.231/D: ⏳ 等待0.3秒后进行下一次循环...
00:36:08.532/D: 🔄 开始第2次看广告循环...
00:36:08.533/D: 🔍 检查是否返回游戏开始界面...
00:36:08.603/D: ⚠️ 无法读取游戏开始界面图片
00:36:08.612/D: ⏰ 等待3秒后开始识别...
00:36:10.850/D: 触摸开始: 16.875 702.2222290039062
00:36:10.851/D: 触摸结束，拖拽状态: false
00:36:10.913/D: 悬浮球被点击（非拖拽）
00:36:10.914/D: 主悬浮球被点击
00:36:10.945/D: 开始展开悬浮球菜单...
00:36:11.259/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:36:11.260/D: 菜单图标应用成功: 开始
00:36:11.262/D: 菜单事件绑定成功: 开始
00:36:11.262/D: 菜单图标创建成功: 开始
00:36:11.375/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:36:11.377/D: 菜单图标应用成功: 停止
00:36:11.378/D: 菜单事件绑定成功: 停止
00:36:11.379/D: 菜单图标创建成功: 停止
00:36:11.520/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:36:11.521/D: 菜单图标应用成功: 主页
00:36:11.523/D: 菜单事件绑定成功: 主页
00:36:11.523/D: 菜单图标创建成功: 主页
00:36:11.601/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:36:11.602/D: 菜单图标应用成功: 日志
00:36:11.603/D: 菜单事件绑定成功: 日志
00:36:11.605/D: 菜单图标创建成功: 日志
00:36:11.605/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:36:11.615/D: 📸 开始截图识别
00:36:11.638/D: 🔍 检查是否在游戏开始界面...
00:36:11.640/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:36:11.641/D: 🚀 启动统一识别符号引擎...
00:36:13.121/D: 菜单被点击: 停止
00:36:13.165/D: 🛑 悬浮球：准备停止算数程序...
00:36:13.165/D: 🛑 收到停止脚本请求
00:36:13.166/D: ✅ 悬浮球：算数程序停止请求已发送
00:36:13.505/D: 开始收起悬浮球菜单...
00:36:13.510/D: 悬浮球菜单收起成功
00:36:15.272/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:36:16.397/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:36:17.725/D:    ├─ 轮廓检测结果为空，跳过合并
00:36:17.740/D:    🏆 总耗时: 6093ms，最终识别到: 4 个符号
00:36:17.757/D: 
📋 符号识别结果:
00:36:17.758/D:   🔣 识别到符号: 4 个
00:36:17.761/D: 🔣 符号详情: V×Login(454,56) 中(278,89) XA(313,87) R(231,777)
00:36:17.762/D: ❌ 未找到可点击的符号
00:36:17.763/D: 🎯 第八步：检查游戏状态...
00:36:17.776/D: 🔍 当前应用包名: com.winrgames.brainbattle
00:36:17.777/D: ✅ 算数游戏已在前台
00:36:17.777/D: ⏳ 等待2秒，让界面稳定...
00:36:19.784/D: ✅ 等待完成，游戏状态检查完毕
00:36:19.785/D: ⏳ 等待0.3秒后进行下一次循环...
00:36:20.086/D: 🔄 开始第3次看广告循环...
00:36:20.087/D: 🔍 检查是否返回游戏开始界面...
00:36:20.170/D: ⚠️ 无法读取游戏开始界面图片
00:36:20.192/D: ⏰ 等待3秒后开始识别...
00:36:20.627/D: 触摸开始: 15.75 689.7777709960938
00:36:20.633/D: 触摸结束，拖拽状态: false
00:36:20.703/D: 悬浮球被点击（非拖拽）
00:36:20.704/D: 主悬浮球被点击
00:36:20.783/D: 开始展开悬浮球菜单...
00:36:21.376/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:36:21.377/D: 菜单图标应用成功: 开始
00:36:21.378/D: 菜单事件绑定成功: 开始
00:36:21.379/D: 菜单图标创建成功: 开始
00:36:21.459/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:36:21.460/D: 菜单图标应用成功: 停止
00:36:21.462/D: 菜单事件绑定成功: 停止
00:36:21.463/D: 菜单图标创建成功: 停止
00:36:21.572/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:36:21.572/D: 菜单图标应用成功: 主页
00:36:21.573/D: 菜单事件绑定成功: 主页
00:36:21.574/D: 菜单图标创建成功: 主页
00:36:21.618/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:36:21.620/D: 菜单图标应用成功: 日志
00:36:21.622/D: 菜单事件绑定成功: 日志
00:36:21.622/D: 菜单图标创建成功: 日志
00:36:21.623/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:36:23.086/D: 菜单被点击: 停止
00:36:23.101/D: 🛑 悬浮球：准备停止算数程序...
00:36:23.102/D: 🛑 收到停止脚本请求
00:36:23.103/D: ✅ 悬浮球：算数程序停止请求已发送
00:36:23.195/D: 📸 开始截图识别
00:36:23.216/D: 🔍 检查是否在游戏开始界面...
00:36:23.223/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:36:23.225/D: 🚀 启动统一识别符号引擎...
00:36:23.414/D: 开始收起悬浮球菜单...
00:36:23.415/D: 悬浮球菜单收起成功
00:36:25.139/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:36:27.287/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:36:28.928/D:    🏆 总耗时: 5703ms，最终识别到: 3 个符号
00:36:28.930/D: 
📋 符号识别结果:
00:36:28.931/D:   🔣 识别到符号: 3 个
00:36:28.932/D: 🔣 符号详情: V×Login(453,56) 台(29,796) X(63,873)
00:36:28.933/D: ❌ 未找到可点击的符号
00:36:28.935/D: 🎯 第八步：检查游戏状态...
00:36:28.949/D: 🔍 当前应用包名: com.winrgames.brainbattle
00:36:28.951/D: ✅ 算数游戏已在前台
00:36:28.954/D: ⏳ 等待2秒，让界面稳定...
00:36:30.956/D: ✅ 等待完成，游戏状态检查完毕
00:36:30.957/D: ⏳ 等待0.3秒后进行下一次循环...
00:36:31.260/D: 🔄 开始第4次看广告循环...
00:36:31.262/D: 🔍 检查是否返回游戏开始界面...
00:36:31.281/D: ⚠️ 无法读取游戏开始界面图片
00:36:31.284/D: ⏰ 等待3秒后开始识别...
00:36:32.956/D: 触摸开始: 24.75 686.2222290039062
00:36:33.049/D: 触摸结束，拖拽状态: false
00:36:33.123/D: 悬浮球被点击（非拖拽）
00:36:33.123/D: 主悬浮球被点击
00:36:33.138/D: 开始展开悬浮球菜单...
00:36:33.221/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:36:33.222/D: 菜单图标应用成功: 开始
00:36:33.224/D: 菜单事件绑定成功: 开始
00:36:33.225/D: 菜单图标创建成功: 开始
00:36:33.278/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:36:33.279/D: 菜单图标应用成功: 停止
00:36:33.280/D: 菜单事件绑定成功: 停止
00:36:33.281/D: 菜单图标创建成功: 停止
00:36:33.325/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:36:33.325/D: 菜单图标应用成功: 主页
00:36:33.327/D: 菜单事件绑定成功: 主页
00:36:33.329/D: 菜单图标创建成功: 主页
00:36:33.398/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:36:33.400/D: 菜单图标应用成功: 日志
00:36:33.401/D: 菜单事件绑定成功: 日志
00:36:33.402/D: 菜单图标创建成功: 日志
00:36:33.403/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:36:34.286/D: 📸 开始截图识别
00:36:34.287/D: 菜单被点击: 停止
00:36:34.295/D: 🔍 检查是否在游戏开始界面...
00:36:34.297/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:36:34.299/D: 🛑 悬浮球：准备停止算数程序...
00:36:34.300/D: 🚀 启动统一识别符号引擎...
00:36:34.300/D: 🛑 收到停止脚本请求
00:36:34.301/D: ✅ 悬浮球：算数程序停止请求已发送
00:36:34.608/D: 开始收起悬浮球菜单...
00:36:34.609/D: 悬浮球菜单收起成功
00:36:36.324/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:36:36.851/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:36:37.871/D:    ├─ 轮廓检测结果为空，跳过合并
00:36:37.874/D:    🏆 总耗时: 3574ms，最终识别到: 3 个符号
00:36:37.875/D: 
📋 符号识别结果:
00:36:37.876/D:   🔣 识别到符号: 3 个
00:36:37.878/D: 🔣 符号详情: V×Login(453,56) Xx(278,82) hEn(282,777)
00:36:37.879/D: ❌ 未找到可点击的符号
00:36:37.880/D: 🎯 第八步：检查游戏状态...
00:36:37.882/D: 🔍 当前应用包名: com.winrgames.brainbattle
00:36:37.883/D: ✅ 算数游戏已在前台
00:36:37.884/D: ⏳ 等待2秒，让界面稳定...
00:36:39.739/D: 触摸开始: 20.8125 686.2222290039062
00:36:39.806/D: 触摸结束，拖拽状态: false
00:36:39.858/D: 悬浮球被点击（非拖拽）
00:36:39.859/D: 主悬浮球被点击
00:36:39.884/D: 开始展开悬浮球菜单...
00:36:39.885/D: ✅ 等待完成，游戏状态检查完毕
00:36:39.886/D: ⏳ 等待0.3秒后进行下一次循环...
00:36:39.940/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:36:39.941/D: 菜单图标应用成功: 开始
00:36:39.943/D: 菜单事件绑定成功: 开始
00:36:39.944/D: 菜单图标创建成功: 开始
00:36:39.985/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:36:39.985/D: 菜单图标应用成功: 停止
00:36:39.987/D: 菜单事件绑定成功: 停止
00:36:39.989/D: 菜单图标创建成功: 停止
00:36:40.028/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:36:40.029/D: 菜单图标应用成功: 主页
00:36:40.030/D: 菜单事件绑定成功: 主页
00:36:40.030/D: 菜单图标创建成功: 主页
00:36:40.080/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:36:40.081/D: 菜单图标应用成功: 日志
00:36:40.082/D: 菜单事件绑定成功: 日志
00:36:40.083/D: 菜单图标创建成功: 日志
00:36:40.084/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:36:40.187/D: 🔄 开始第5次看广告循环...
00:36:40.189/D: 🔍 检查是否返回游戏开始界面...
00:36:40.207/D: ⚠️ 无法读取游戏开始界面图片
00:36:40.209/D: ⏰ 等待3秒后开始识别...
00:36:41.009/D: 菜单被点击: 停止
00:36:41.032/D: 🛑 悬浮球：准备停止算数程序...
00:36:41.033/D: 🛑 收到停止脚本请求
00:36:41.033/D: ✅ 悬浮球：算数程序停止请求已发送
00:36:41.335/D: 开始收起悬浮球菜单...
00:36:41.336/D: 悬浮球菜单收起成功
00:36:43.038/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:36:43.211/D: 📸 开始截图识别
00:36:43.244/D: 🔍 检查是否在游戏开始界面...
00:36:43.246/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:36:43.254/D: 🚀 启动统一识别符号引擎...
00:36:45.912/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:36:46.638/D:    ├─ 轮廓检测结果为空，跳过合并
00:36:46.640/D:    🏆 总耗时: 3386ms，最终识别到: 2 个符号
00:36:46.643/D: 
📋 符号识别结果:
00:36:46.644/D:   🔣 识别到符号: 2 个
00:36:46.645/D: 🔣 符号详情: V×Login(453,56) 请(265,888)
00:36:46.647/D: ❌ 未找到可点击的符号
00:36:46.648/D: 🎯 第八步：检查游戏状态...
00:36:46.650/D: 🔍 当前应用包名: com.android.launcher3
00:36:46.651/D: ⚠️ 算数游戏不在前台，正在切换回算数游戏...
00:36:46.673/D: ✅ 已切换回算数游戏应用
00:36:46.674/D: ⏳ 等待2秒，让界面稳定...
00:36:48.676/D: ✅ 等待完成，游戏状态检查完毕
00:36:48.702/D: ⏳ 等待0.3秒后进行下一次循环...
00:36:49.009/D: 🔄 开始第6次看广告循环...
00:36:49.010/D: 🔍 检查是否返回游戏开始界面...
00:36:49.160/D: ⚠️ 无法读取游戏开始界面图片
00:36:49.166/D: ⏰ 等待3秒后开始识别...
00:36:52.175/D: 📸 开始截图识别
00:36:52.184/D: 🔍 检查是否在游戏开始界面...
00:36:52.185/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:36:52.186/D: 🚀 启动统一识别符号引擎...
00:36:55.320/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:36:55.790/D: 🚀 准备启动算数程序...
00:36:55.791/D: 🔄 延迟加载算数程序模块...
00:36:55.810/D: ✅ 算数程序模块加载成功
00:36:55.811/D: ⚠️ 脚本已在运行，当前步骤: 第4步：首次提现教程
00:36:56.321/D:    🏆 总耗时: 4130ms，最终识别到: 11 个符号
00:36:56.322/D: 
📋 符号识别结果:
00:36:56.324/D:   🔣 识别到符号: 11 个
00:36:56.329/D: 🔣 符号详情: 12:36(39,22) L(121,23) V6(481,21) 三(41,82) 主页(270,79) 运行(141,826) 口停(387,825) 警告：脚本未运行(269,887) 8(90,916) F(444,21) 警告：脚本未运行(268,887)
00:36:56.332/D: ❌ 未找到可点击的符号
00:36:56.333/D: 🎯 第八步：检查游戏状态...
00:36:56.335/D: 🔍 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:36:56.335/D: ⚠️ 算数游戏不在前台，正在切换回算数游戏...
00:36:56.428/D: ✅ 已切换回算数游戏应用
00:36:56.429/D: ⏳ 等待2秒，让界面稳定...
00:36:58.449/D: ✅ 等待完成，游戏状态检查完毕
00:36:58.455/D: ⏳ 等待0.3秒后进行下一次循环...
00:36:58.761/D: 🔄 开始第7次看广告循环...
00:36:58.761/D: 🔍 检查是否返回游戏开始界面...
00:36:58.866/D: ⚠️ 无法读取游戏开始界面图片
00:36:58.882/D: ⏰ 等待3秒后开始识别...
00:37:01.886/D: 📸 开始截图识别
00:37:01.899/D: 🔍 检查是否在游戏开始界面...
00:37:01.900/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:37:01.902/D: 🚀 启动统一识别符号引擎...
00:37:04.276/D: 触摸开始: 21.9375 684.4444580078125
00:37:04.302/D: 触摸结束，拖拽状态: false
00:37:04.360/D: 悬浮球被点击（非拖拽）
00:37:04.361/D: 主悬浮球被点击
00:37:04.406/D: 开始展开悬浮球菜单...
00:37:04.554/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:37:04.555/D: 菜单图标应用成功: 开始
00:37:04.555/D: 菜单事件绑定成功: 开始
00:37:04.556/D: 菜单图标创建成功: 开始
00:37:04.585/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:37:04.585/D: 菜单图标应用成功: 停止
00:37:04.586/D: 菜单事件绑定成功: 停止
00:37:04.587/D: 菜单图标创建成功: 停止
00:37:04.645/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:37:04.645/D: 菜单图标应用成功: 主页
00:37:04.646/D: 菜单事件绑定成功: 主页
00:37:04.647/D: 菜单图标创建成功: 主页
00:37:04.697/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:37:04.699/D: 菜单图标应用成功: 日志
00:37:04.702/D: 菜单事件绑定成功: 日志
00:37:04.708/D: 菜单图标创建成功: 日志
00:37:04.710/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:37:05.301/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:37:06.031/D:    ├─ 轮廓检测结果为空，跳过合并
00:37:06.035/D:    🏆 总耗时: 4131ms，最终识别到: 10 个符号
00:37:06.036/D: 
📋 符号识别结果:
00:37:06.037/D:   🔣 识别到符号: 10 个
00:37:06.041/D: 🔣 符号详情: 12:37(36,21) L(120,22) V6(481,21) 三(41,82) 主页(270,79) 运行(139,826) 口停(388,826) 校(91,917) 公(274,916) F(444,21)
00:37:06.042/D: ❌ 未找到可点击的符号
00:37:06.043/D: 🎯 第八步：检查游戏状态...
00:37:06.045/D: 🔍 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:37:06.045/D: ⚠️ 算数游戏不在前台，正在切换回算数游戏...
00:37:06.067/D: ✅ 已切换回算数游戏应用
00:37:06.068/D: ⏳ 等待2秒，让界面稳定...
00:37:06.613/D: 菜单被点击: 停止
00:37:06.641/D: 🛑 悬浮球：准备停止算数程序...
00:37:06.642/D: 🛑 收到停止脚本请求
00:37:06.644/D: ✅ 悬浮球：算数程序停止请求已发送
00:37:07.024/D: 开始收起悬浮球菜单...
00:37:07.025/D: 悬浮球菜单收起成功
00:37:08.069/D: ✅ 等待完成，游戏状态检查完毕
00:37:08.070/D: ⏳ 等待0.3秒后进行下一次循环...
00:37:08.371/D: 🔄 开始第8次看广告循环...
00:37:08.372/D: 🔍 检查是否返回游戏开始界面...
00:37:08.383/D: ⚠️ 无法读取游戏开始界面图片
00:37:08.387/D: ⏰ 等待3秒后开始识别...
00:37:08.743/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:37:11.388/D: 📸 开始截图识别
00:37:11.396/D: 🔍 检查是否在游戏开始界面...
00:37:11.398/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:37:11.399/D: 🚀 启动统一识别符号引擎...
00:37:14.031/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:37:14.889/D:    ├─ 轮廓检测结果为空，跳过合并
00:37:14.890/D:    🏆 总耗时: 3491ms，最终识别到: 2 个符号
00:37:14.891/D: 
📋 符号识别结果:
00:37:14.892/D:   🔣 识别到符号: 2 个
00:37:14.893/D: 🔣 符号详情: V×Login(453,56) 行(261,85)
00:37:14.893/D: ❌ 未找到可点击的符号
00:37:14.893/D: 🎯 第八步：检查游戏状态...
00:37:14.894/D: 🔍 当前应用包名: com.winrgames.brainbattle
00:37:14.894/D: ✅ 算数游戏已在前台
00:37:14.895/D: ⏳ 等待2秒，让界面稳定...
00:37:16.896/D: ✅ 等待完成，游戏状态检查完毕
00:37:16.897/D: ⏳ 等待0.3秒后进行下一次循环...
00:37:17.198/D: 🔄 开始第9次看广告循环...
00:37:17.201/D: 🔍 检查是否返回游戏开始界面...
00:37:17.210/D: ⚠️ 无法读取游戏开始界面图片
00:37:17.211/D: ⏰ 等待3秒后开始识别...
00:37:20.220/D: 📸 开始截图识别
00:37:20.228/D: 🔍 检查是否在游戏开始界面...
00:37:20.229/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:37:20.229/D: 🚀 启动统一识别符号引擎...
00:37:23.598/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:37:24.839/D:    🏆 总耗时: 4610ms，最终识别到: 11 个符号
00:37:24.840/D: 
📋 符号识别结果:
00:37:24.841/D:   🔣 识别到符号: 11 个
00:37:24.843/D: 🔣 符号详情: 12:37(37,22) 口O(120,23) 省(451,21) VE(495,21) C(457,191) lay游戏(457,233) C(454,317) 2(23,871) F(444,19) ay游戏(456,234) 3(453,317)
00:37:24.844/D: ❌ 未找到可点击的符号
00:37:24.845/D: 🎯 第八步：检查游戏状态...
00:37:24.846/D: 🔍 当前应用包名: org.autojs.autoxjs.ozobi.v6
00:37:24.847/D: ⚠️ 算数游戏不在前台，正在切换回算数游戏...
00:37:24.888/D: ✅ 已切换回算数游戏应用
00:37:24.889/D: ⏳ 等待2秒，让界面稳定...
00:37:26.890/D: ✅ 等待完成，游戏状态检查完毕
00:37:26.891/D: ⏳ 等待0.3秒后进行下一次循环...
00:37:27.192/D: 🔄 开始第10次看广告循环...
00:37:27.193/D: 🔍 检查是否返回游戏开始界面...
00:37:27.206/D: ⚠️ 无法读取游戏开始界面图片
00:37:27.211/D: ⏰ 等待3秒后开始识别...
00:37:30.214/D: 📸 开始截图识别
00:37:30.223/D: 🔍 检查是否在游戏开始界面...
00:37:30.224/D: ⚠️ 无法读取游戏开始界面图片: ../../assets/算数游戏/新手教程图片/点击分数.png
00:37:30.225/D: 🚀 启动统一识别符号引擎...
00:37:30.341/D: 触摸开始: 15.1875 693.3333129882812
00:37:30.420/D: 触摸结束，拖拽状态: false
00:37:30.488/D: 悬浮球被点击（非拖拽）
00:37:30.489/D: 主悬浮球被点击
00:37:30.528/D: 开始展开悬浮球菜单...
00:37:30.643/D: 悬浮球图标应用成功: 开始图标 媒体.播放
00:37:30.643/D: 菜单图标应用成功: 开始
00:37:30.645/D: 菜单事件绑定成功: 开始
00:37:30.645/D: 菜单图标创建成功: 开始
00:37:30.693/D: 悬浮球图标应用成功: 停止图标 媒体.停止
00:37:30.694/D: 菜单图标应用成功: 停止
00:37:30.695/D: 菜单事件绑定成功: 停止
00:37:30.695/D: 菜单图标创建成功: 停止
00:37:30.747/D: 悬浮球图标应用成功: 主页图标 导航.主页
00:37:30.748/D: 菜单图标应用成功: 主页
00:37:30.749/D: 菜单事件绑定成功: 主页
00:37:30.750/D: 菜单图标创建成功: 主页
00:37:30.801/D: 悬浮球图标应用成功: 日志图标 文件.文档
00:37:30.802/D: 菜单图标应用成功: 日志
00:37:30.804/D: 菜单事件绑定成功: 日志
00:37:30.805/D: 菜单图标创建成功: 日志
00:37:30.806/D: 悬浮球菜单展开成功，共创建 4 个菜单
00:37:32.111/D: 菜单被点击: 停止
00:37:32.136/D: 🛑 悬浮球：准备停止算数程序...
00:37:32.137/D: 🛑 收到停止脚本请求
00:37:32.139/D: ✅ 悬浮球：算数程序停止请求已发送
00:37:32.454/D: 开始收起悬浮球菜单...
00:37:32.455/D: 悬浮球菜单收起成功
00:37:33.052/E: 图片模板目录不存在: ../../assets/算数游戏/广告/右广告
00:37:34.009/D:    ├─ 轮廓检测结果为空，跳过合并
00:37:34.012/D:    🏆 总耗时: 3786ms，最终识别到: 1 个符号
00:37:34.014/D: 
📋 符号识别结果:
00:37:34.015/D:   🔣 识别到符号: 1 个
00:37:34.016/D: 🔣 符号详情: V×Login(453,56)
00:37:34.017/D: ❌ 未找到可点击的符号
00:37:34.018/D: 🎯 第八步：检查游戏状态...
00:37:34.018/D: 🔍 当前应用包名: com.winrgames.brainbattle
00:37:34.018/D: ✅ 算数游戏已在前台
00:37:34.019/D: ⏳ 等待2秒，让界面稳定...
00:37:34.256/D: ⚠️ 悬浮球：算数程序仍在运行，当前步骤: 第4步：首次提现教程
00:37:35.858/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/main.js ]运行结束，用时146.133000秒
00:37:35.931/D: 应用退出，清理抽屉资源...
00:37:35.933/D: 抽屉资源清理完成
00:37:35.934/D: 应用正在退出...
00:37:35.934/D: 🛑 准备停止算数程序...
00:37:35.935/D: 🛑 收到停止脚本请求
00:37:35.938/D: ✅ 算数程序停止请求已发送
00:37:35.940/D: 菜单未展开，无需收起
00:37:35.941/D: 悬浮球隐藏成功
00:37:35.943/D: 悬浮球销毁成功
00:37:35.946/D: 所有悬浮球事件已解绑
00:37:35.948/D: 悬浮球停止成功
00:37:35.949/D: 所有悬浮球事件已解绑
00:37:35.951/D: 悬浮球系统销毁成功
00:37:35.953/D: 悬浮球系统已清理
00:37:35.973/D: 图标管理器资源已清理
00:37:35.974/D: 图标管理器已清理
00:37:35.975/D: [信息] 应用正常退出
00:37:35.975/D: 应用退出完成
00:37:35.989/D: ❌ 看广告功能执行出错: JavaException: com.stardust.autojs.runtime.exception.ScriptInterruptedException: null
00:37:35.990/D: 🛑 检测到停止请求，准备停止脚本
00:37:35.990/D: ❌ 悬浮球：算数程序执行失败
00:37:36.013/E: 线程中申请截图权限失败: { JavaException: java.lang.InterruptedException: null
	at /android_asset/modules/__images__.js:165
	at /storage/emulated/0/脚本/magic/ui/菜单抽屉页/侧滑抽屉.js:1742

  fileName: '/android_asset/modules/__images__.js',
  lineNumber: 165 }
00:37:36.013/E: 线程中申请截图权限失败: { JavaException: java.lang.InterruptedException: null
	at /android_asset/modules/__images__.js:165
	at /storage/emulated/0/脚本/magic/ui/菜单抽屉页/侧滑抽屉.js:1742

  fileName: '/android_asset/modules/__images__.js',
  lineNumber: 165 }
00:37:36.013/E: 线程中申请截图权限失败: { JavaException: java.lang.InterruptedException: null
	at /android_asset/modules/__images__.js:165
	at /storage/emulated/0/脚本/magic/ui/菜单抽屉页/侧滑抽屉.js:1742

  fileName: '/android_asset/modules/__images__.js',
  lineNumber: 165 }
00:37:36.015/E: 线程中申请截图权限失败: { JavaException: java.lang.InterruptedException: null
	at /android_asset/modules/__images__.js:165
	at /storage/emulated/0/脚本/magic/ui/菜单抽屉页/侧滑抽屉.js:1742

  fileName: '/android_asset/modules/__images__.js',
  lineNumber: 165 }
00:37:36.016/E: 线程中申请截图权限失败: { JavaException: java.lang.InterruptedException: null
	at /android_asset/modules/__images__.js:165
	at /storage/emulated/0/脚本/magic/ui/菜单抽屉页/侧滑抽屉.js:1742

  fileName: '/android_asset/modules/__images__.js',
  lineNumber: 165 }
00:37:36.020/D: 应用退出，清理抽屉资源...
00:37:36.020/D: 抽屉资源清理完成
00:37:36.022/D: 应用正在退出...
00:37:36.027/D: 🛑 准备停止算数程序...
00:37:36.034/D: ⚠️ 脚本未在运行
00:37:36.048/D: 图标管理器资源已清理
00:37:36.051/D: 图标管理器已清理
00:37:36.053/D: [信息] 应用正常退出
00:37:36.055/D: 应用退出完成
00:37:36.188/D: 截图权限开关被点击，状态: 关闭
00:37:36.189/D: 用户要求关闭截图权限...
00:37:36.191/D: 配置已保存: 权限状态.截图权限 = false
00:37:36.192/D: 截图权限已关闭
00:37:36.193/D: 截图权限开关被点击，状态: 关闭
00:37:36.193/D: 用户要求关闭截图权限...
00:37:36.195/D: 配置已保存: 权限状态.截图权限 = false
00:37:36.196/D: 截图权限已关闭
00:37:36.197/D: 截图权限开关被点击，状态: 关闭
00:37:36.198/D: 用户要求关闭截图权限...
00:37:36.199/D: 配置已保存: 权限状态.截图权限 = false
00:37:36.200/D: 截图权限已关闭
