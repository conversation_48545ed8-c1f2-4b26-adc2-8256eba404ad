:55:40.812/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/新手教程图片/开始界面.png
05:55:40.813/D: 🔍 图片路径: /storage/emulated/0/assets/算数游戏/新手教程图片/开始界面.png
05:55:40.814/D: 🔍 开始判断点击图片: /storage/emulated/0/assets/算数游戏/新手教程图片/开始界面.png (相似度: 0.7)
05:55:40.834/D: 🎯 在指定区域判断点击图片...
05:55:40.836/D: ❌ 图片读取失败: /storage/emulated/0/assets/算数游戏/新手教程图片/开始界面.png
05:55:40.837/D: ⚠️ 第一步未找到按钮：可能已经在开始界面了
05:55:40.837/D: ⏳ 随机延时 131 毫秒...
05:55:40.970/D: 📝 第二步：点击开始游戏按钮
05:55:40.971/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:40.972/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:40.972/D: 🖼️ 构建图片路径: 新手教程图片/开始按钮.png → /storage/emulated/0/assets/算数游戏/新手教程图片/开始按钮.png
05:55:40.973/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/新手教程图片/开始按钮.png
05:55:40.973/D: 🔍 图片路径: /storage/emulated/0/assets/算数游戏/新手教程图片/开始按钮.png
05:55:40.974/D: 🔍 开始判断点击图片: /storage/emulated/0/assets/算数游戏/新手教程图片/开始按钮.png (相似度: 0.7)
05:55:40.980/D: 🎯 在指定区域判断点击图片...
05:55:40.982/D: ❌ 图片读取失败: /storage/emulated/0/assets/算数游戏/新手教程图片/开始按钮.png
05:55:40.984/D: ⚠️ 第二步未找到按钮：可能已经开始游戏了
05:55:40.985/D: ✅ 点击开始界面完成
05:55:40.988/D: ⏳ 随机等待 126 毫秒后开始算数游戏识别和计算
05:55:41.117/D: 📋 步骤4: 开始算数游戏识别和计算
05:55:41.117/D: 🧮 启动三区域公式识别引擎
05:55:41.124/D: 🔍 第一步：检测是否在开始界面...
05:55:41.124/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.127/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.128/D: 🖼️ 构建图片路径: 新手教程图片/点击分数.png → /storage/emulated/0/assets/算数游戏/新手教程图片/点击分数.png
05:55:41.129/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/新手教程图片/点击分数.png
05:55:41.129/D: 🔍 开始查找图片: /storage/emulated/0/assets/算数游戏/新手教程图片/点击分数.png (相似度: 0.9)
05:55:41.144/D: 🎯 在指定区域查找图片...
05:55:41.145/D: ❌ 图片读取失败: /storage/emulated/0/assets/算数游戏/新手教程图片/点击分数.png
05:55:41.147/D: ⚠️ 未检测到开始界面，但继续执行表达式识别
05:55:41.148/D: ⏳ 延时100毫秒后开始循环识别...
05:55:41.250/D: 🎯 本次执行循环次数: 107
05:55:41.251/D: 🎯 故意点错次数: 104
05:55:41.263/D: ------------------------截图功能--------------------------
05:55:41.264/D: ✅ 全屏截图成功，开始三区域识别
05:55:41.266/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.267/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.268/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.268/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.270/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.271/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.272/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.272/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.274/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.276/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.277/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.278/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.279/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.280/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.281/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.281/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.282/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.283/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.283/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.284/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.285/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.286/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.288/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.288/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.290/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.291/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.291/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.292/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.293/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.294/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.295/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.295/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.296/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.297/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.298/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.298/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.300/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.300/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.301/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.302/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.303/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.303/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.305/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.305/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.306/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.307/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.308/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.308/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.310/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.311/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.312/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.313/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.314/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.315/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.316/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.317/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
05:55:41.318/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.319/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.320/D: 🖼️ 构建图片路径: 6_1.png → /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.321/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.322/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.322/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.323/D: 🖼️ 构建图片路径: 7.png → /storage/emulated/0/assets/算数游戏/7.png
05:55:41.324/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7.png
05:55:41.325/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.327/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.328/D: 🖼️ 构建图片路径: 7_1.png → /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.331/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.332/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.333/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.334/D: 🖼️ 构建图片路径: 8.png → /storage/emulated/0/assets/算数游戏/8.png
05:55:41.335/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8.png
05:55:41.336/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.336/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.337/D: 🖼️ 构建图片路径: 8_1.png → /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.338/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.339/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.340/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.340/D: 🖼️ 构建图片路径: 9.png → /storage/emulated/0/assets/算数游戏/9.png
05:55:41.341/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9.png
05:55:41.342/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.343/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.344/D: 🖼️ 构建图片路径: 9_1.png → /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.344/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.346/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.346/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.347/D: 🖼️ 构建图片路径: 9_2.png → /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.347/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.349/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.350/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.351/D: 🖼️ 构建图片路径: +.png → /storage/emulated/0/assets/算数游戏/+.png
05:55:41.352/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/+.png
05:55:41.353/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.354/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.355/D: 🖼️ 构建图片路径: 减号.png → /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.355/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.356/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.357/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.358/D: 🖼️ 构建图片路径: x.png → /storage/emulated/0/assets/算数游戏/x.png
05:55:41.359/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/x.png
05:55:41.360/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.361/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.362/D: 🖼️ 构建图片路径: 除.png → /storage/emulated/0/assets/算数游戏/除.png
05:55:41.363/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/除.png
05:55:41.365/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.365/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.366/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.367/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.368/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.369/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.370/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.371/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.373/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.374/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.376/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.377/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.378/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.379/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.380/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.381/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.383/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.384/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.384/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.385/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.386/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.386/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.387/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.388/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.389/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.390/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.390/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.391/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.392/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.393/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.393/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.394/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.395/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.396/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.396/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.397/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.398/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.399/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.400/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.401/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.402/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.402/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.403/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.403/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.404/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.405/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.405/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.406/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.407/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.407/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.408/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.408/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.409/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.409/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.410/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.411/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
05:55:41.411/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.412/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.413/D: 🖼️ 构建图片路径: 6_1.png → /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.413/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.414/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.414/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.415/D: 🖼️ 构建图片路径: 7.png → /storage/emulated/0/assets/算数游戏/7.png
05:55:41.416/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7.png
05:55:41.416/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.417/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.417/D: 🖼️ 构建图片路径: 7_1.png → /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.418/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.419/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.420/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.421/D: 🖼️ 构建图片路径: 8.png → /storage/emulated/0/assets/算数游戏/8.png
05:55:41.422/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8.png
05:55:41.423/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.423/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.424/D: 🖼️ 构建图片路径: 8_1.png → /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.425/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.426/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.427/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.428/D: 🖼️ 构建图片路径: 9.png → /storage/emulated/0/assets/算数游戏/9.png
05:55:41.428/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9.png
05:55:41.429/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.430/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.430/D: 🖼️ 构建图片路径: 9_1.png → /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.431/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.432/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.433/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.433/D: 🖼️ 构建图片路径: 9_2.png → /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.434/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.435/D: ------------------------识别结果汇总--------------------------
05:55:41.436/D: ❌ 表达式识别不完整:
05:55:41.436/D:   前数字: 未识别
05:55:41.437/D:   运算符: 未识别
05:55:41.437/D:   后数字: 未识别
05:55:41.438/D: ⚠️ 表达式识别不完整，跳过本次循环，继续下一次 (失败次数: 1/5)
05:55:41.447/D: ------------------------截图功能--------------------------
05:55:41.448/D: ✅ 全屏截图成功，开始三区域识别
05:55:41.450/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.451/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.452/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.452/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.453/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.454/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.454/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.455/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.457/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.457/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.458/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.459/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.461/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.462/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.463/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.463/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.464/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.464/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.465/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.466/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.467/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.468/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.468/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.469/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.470/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.470/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.471/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.472/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.472/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.473/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.473/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.474/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.474/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.475/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.475/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.476/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.477/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.477/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.478/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.478/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.479/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.480/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.480/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.481/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.481/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.482/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.482/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.483/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.484/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.484/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.485/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.487/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.488/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.488/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.489/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.490/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
05:55:41.491/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.491/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.493/D: 🖼️ 构建图片路径: 6_1.png → /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.493/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.494/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.494/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.495/D: 🖼️ 构建图片路径: 7.png → /storage/emulated/0/assets/算数游戏/7.png
05:55:41.496/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7.png
05:55:41.497/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.497/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.498/D: 🖼️ 构建图片路径: 7_1.png → /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.499/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.500/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.500/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.501/D: 🖼️ 构建图片路径: 8.png → /storage/emulated/0/assets/算数游戏/8.png
05:55:41.501/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8.png
05:55:41.502/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.503/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.504/D: 🖼️ 构建图片路径: 8_1.png → /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.504/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.506/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.507/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.508/D: 🖼️ 构建图片路径: 9.png → /storage/emulated/0/assets/算数游戏/9.png
05:55:41.509/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9.png
05:55:41.510/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.510/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.511/D: 🖼️ 构建图片路径: 9_1.png → /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.512/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.513/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.514/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.515/D: 🖼️ 构建图片路径: 9_2.png → /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.515/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.517/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.518/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.518/D: 🖼️ 构建图片路径: +.png → /storage/emulated/0/assets/算数游戏/+.png
05:55:41.519/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/+.png
05:55:41.520/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.520/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.521/D: 🖼️ 构建图片路径: 减号.png → /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.523/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.524/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.524/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.525/D: 🖼️ 构建图片路径: x.png → /storage/emulated/0/assets/算数游戏/x.png
05:55:41.526/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/x.png
05:55:41.527/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.528/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.529/D: 🖼️ 构建图片路径: 除.png → /storage/emulated/0/assets/算数游戏/除.png
05:55:41.529/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/除.png
05:55:41.531/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.532/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.533/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.533/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.535/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.536/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.537/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.537/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.538/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.538/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.539/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.540/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.540/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.541/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.542/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.542/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.543/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.544/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.544/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.545/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.546/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.546/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.546/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.547/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.547/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.548/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.548/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.550/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.551/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.553/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.554/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.555/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.556/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.557/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.559/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.560/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.562/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.563/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.565/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.566/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.567/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.568/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.569/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.570/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.571/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.572/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.573/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.574/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.576/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.577/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.577/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.578/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.580/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.580/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.582/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.583/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
05:55:41.584/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.584/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.585/D: 🖼️ 构建图片路径: 6_1.png → /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.586/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.587/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.588/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.589/D: 🖼️ 构建图片路径: 7.png → /storage/emulated/0/assets/算数游戏/7.png
05:55:41.589/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7.png
05:55:41.590/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.591/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.591/D: 🖼️ 构建图片路径: 7_1.png → /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.592/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.593/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.594/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.594/D: 🖼️ 构建图片路径: 8.png → /storage/emulated/0/assets/算数游戏/8.png
05:55:41.595/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8.png
05:55:41.596/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.597/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.597/D: 🖼️ 构建图片路径: 8_1.png → /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.598/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.599/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.599/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.600/D: 🖼️ 构建图片路径: 9.png → /storage/emulated/0/assets/算数游戏/9.png
05:55:41.600/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9.png
05:55:41.601/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.602/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.602/D: 🖼️ 构建图片路径: 9_1.png → /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.603/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.604/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.605/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.606/D: 🖼️ 构建图片路径: 9_2.png → /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.607/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.608/D: ------------------------识别结果汇总--------------------------
05:55:41.608/D: ❌ 表达式识别不完整:
05:55:41.609/D:   前数字: 未识别
05:55:41.609/D:   运算符: 未识别
05:55:41.610/D:   后数字: 未识别
05:55:41.611/D: ⚠️ 表达式识别不完整，跳过本次循环，继续下一次 (失败次数: 2/5)
05:55:41.623/D: ------------------------截图功能--------------------------
05:55:41.624/D: ✅ 全屏截图成功，开始三区域识别
05:55:41.626/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.627/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.628/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.629/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.630/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.632/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.632/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.633/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.634/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.635/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.636/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.637/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.638/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.638/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.639/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.640/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.641/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.641/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.642/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.643/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.643/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.644/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.644/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.645/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.646/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.646/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.647/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.648/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.648/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.649/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.650/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.650/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.651/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.651/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.652/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.652/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.654/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.654/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.655/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.656/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.657/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.658/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.658/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.659/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.660/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.660/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.661/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.661/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.662/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.663/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.663/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.664/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.665/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.665/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.666/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.666/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
05:55:41.667/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.668/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.669/D: 🖼️ 构建图片路径: 6_1.png → /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.669/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.670/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.670/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.671/D: 🖼️ 构建图片路径: 7.png → /storage/emulated/0/assets/算数游戏/7.png
05:55:41.671/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7.png
05:55:41.672/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.672/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.673/D: 🖼️ 构建图片路径: 7_1.png → /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.673/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.674/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.675/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.675/D: 🖼️ 构建图片路径: 8.png → /storage/emulated/0/assets/算数游戏/8.png
05:55:41.676/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8.png
05:55:41.677/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.677/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.678/D: 🖼️ 构建图片路径: 8_1.png → /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.680/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.681/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.681/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.682/D: 🖼️ 构建图片路径: 9.png → /storage/emulated/0/assets/算数游戏/9.png
05:55:41.683/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9.png
05:55:41.684/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.685/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.686/D: 🖼️ 构建图片路径: 9_1.png → /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.687/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.688/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.689/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.690/D: 🖼️ 构建图片路径: 9_2.png → /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.690/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.692/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.692/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.693/D: 🖼️ 构建图片路径: +.png → /storage/emulated/0/assets/算数游戏/+.png
05:55:41.694/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/+.png
05:55:41.695/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.696/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.697/D: 🖼️ 构建图片路径: 减号.png → /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.697/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.698/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.699/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.700/D: 🖼️ 构建图片路径: x.png → /storage/emulated/0/assets/算数游戏/x.png
05:55:41.701/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/x.png
05:55:41.703/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.703/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.704/D: 🖼️ 构建图片路径: 除.png → /storage/emulated/0/assets/算数游戏/除.png
05:55:41.705/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/除.png
05:55:41.707/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.707/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.708/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.709/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.710/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.711/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.711/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.712/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.713/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.714/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.715/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.715/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.716/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.717/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.717/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.718/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.719/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.719/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.720/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.721/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.722/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.722/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.723/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.724/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.725/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.725/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.726/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.726/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.729/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.729/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.730/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.730/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.731/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.732/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.733/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.733/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.735/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.735/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.735/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.736/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.737/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.737/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.738/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.739/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.739/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.740/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.741/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.741/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.742/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.743/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.743/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.744/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.745/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.745/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.746/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.747/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
05:55:41.748/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.748/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.749/D: 🖼️ 构建图片路径: 6_1.png → /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.749/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.750/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.751/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.751/D: 🖼️ 构建图片路径: 7.png → /storage/emulated/0/assets/算数游戏/7.png
05:55:41.751/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7.png
05:55:41.753/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.753/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.754/D: 🖼️ 构建图片路径: 7_1.png → /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.755/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.756/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.757/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.757/D: 🖼️ 构建图片路径: 8.png → /storage/emulated/0/assets/算数游戏/8.png
05:55:41.758/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8.png
05:55:41.759/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.759/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.760/D: 🖼️ 构建图片路径: 8_1.png → /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.761/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.762/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.762/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.763/D: 🖼️ 构建图片路径: 9.png → /storage/emulated/0/assets/算数游戏/9.png
05:55:41.764/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9.png
05:55:41.764/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.766/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.766/D: 🖼️ 构建图片路径: 9_1.png → /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.767/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.768/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.768/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.768/D: 🖼️ 构建图片路径: 9_2.png → /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.769/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.770/D: ------------------------识别结果汇总--------------------------
05:55:41.770/D: ❌ 表达式识别不完整:
05:55:41.771/D:   前数字: 未识别
05:55:41.771/D:   运算符: 未识别
05:55:41.772/D:   后数字: 未识别
05:55:41.772/D: ⚠️ 表达式识别不完整，跳过本次循环，继续下一次 (失败次数: 3/5)
05:55:41.780/D: ------------------------截图功能--------------------------
05:55:41.781/D: ✅ 全屏截图成功，开始三区域识别
05:55:41.782/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.783/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.783/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.784/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.785/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.786/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.786/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.787/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.788/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.789/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.790/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.791/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.792/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.792/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.793/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.794/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.795/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.795/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.796/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.797/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.798/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.798/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.799/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.800/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.801/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.802/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.802/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.803/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.804/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.804/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.805/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.806/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.806/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.807/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.807/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.808/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.809/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.809/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.810/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.810/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.811/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.812/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.812/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.813/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.813/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.814/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.815/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.815/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.816/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.816/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.817/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.817/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.818/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.819/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.820/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.820/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
05:55:41.821/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.821/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.822/D: 🖼️ 构建图片路径: 6_1.png → /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.823/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6_1.png
05:55:41.823/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.824/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.824/D: 🖼️ 构建图片路径: 7.png → /storage/emulated/0/assets/算数游戏/7.png
05:55:41.825/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7.png
05:55:41.826/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.827/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.827/D: 🖼️ 构建图片路径: 7_1.png → /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.827/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/7_1.png
05:55:41.828/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.829/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.829/D: 🖼️ 构建图片路径: 8.png → /storage/emulated/0/assets/算数游戏/8.png
05:55:41.830/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8.png
05:55:41.831/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.832/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.833/D: 🖼️ 构建图片路径: 8_1.png → /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.833/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/8_1.png
05:55:41.834/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.835/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.835/D: 🖼️ 构建图片路径: 9.png → /storage/emulated/0/assets/算数游戏/9.png
05:55:41.836/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9.png
05:55:41.837/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.837/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.838/D: 🖼️ 构建图片路径: 9_1.png → /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.840/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_1.png
05:55:41.841/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.842/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.843/D: 🖼️ 构建图片路径: 9_2.png → /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.843/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/9_2.png
05:55:41.845/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.846/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.846/D: 🖼️ 构建图片路径: +.png → /storage/emulated/0/assets/算数游戏/+.png
05:55:41.847/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/+.png
05:55:41.847/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.848/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.848/D: 🖼️ 构建图片路径: 减号.png → /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.849/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/减号.png
05:55:41.850/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.851/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.851/D: 🖼️ 构建图片路径: x.png → /storage/emulated/0/assets/算数游戏/x.png
05:55:41.852/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/x.png
05:55:41.853/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.853/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.854/D: 🖼️ 构建图片路径: 除.png → /storage/emulated/0/assets/算数游戏/除.png
05:55:41.855/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/除.png
05:55:41.856/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.856/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.857/D: 🖼️ 构建图片路径: 0.png → /storage/emulated/0/assets/算数游戏/0.png
05:55:41.858/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0.png
05:55:41.859/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.859/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.860/D: 🖼️ 构建图片路径: 0_1.png → /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.861/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/0_1.png
05:55:41.861/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.862/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.862/D: 🖼️ 构建图片路径: 1.png → /storage/emulated/0/assets/算数游戏/1.png
05:55:41.863/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1.png
05:55:41.863/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.864/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.865/D: 🖼️ 构建图片路径: 1_1.png → /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.866/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/1_1.png
05:55:41.867/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.867/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.868/D: 🖼️ 构建图片路径: 2.png → /storage/emulated/0/assets/算数游戏/2.png
05:55:41.869/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2.png
05:55:41.870/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.870/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.871/D: 🖼️ 构建图片路径: 2_1.png → /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.871/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_1.png
05:55:41.872/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.873/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.873/D: 🖼️ 构建图片路径: 2_3.png → /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.874/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/2_3.png
05:55:41.874/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.875/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.876/D: 🖼️ 构建图片路径: 3.png → /storage/emulated/0/assets/算数游戏/3.png
05:55:41.877/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3.png
05:55:41.878/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.878/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.879/D: 🖼️ 构建图片路径: 3_1.png → /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.879/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/3_1.png
05:55:41.880/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.881/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.882/D: 🖼️ 构建图片路径: 4.png → /storage/emulated/0/assets/算数游戏/4.png
05:55:41.883/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4.png
05:55:41.884/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.885/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.885/D: 🖼️ 构建图片路径: 4_1.png → /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.886/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/4_1.png
05:55:41.887/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.887/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.888/D: 🖼️ 构建图片路径: 5.png → /storage/emulated/0/assets/算数游戏/5.png
05:55:41.888/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5.png
05:55:41.889/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.890/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.891/D: 🖼️ 构建图片路径: 5_1.png → /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.891/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/5_1.png
05:55:41.892/D: 🔍 当前脚本路径: /storage/emulated/0/脚本/magic/main.js
05:55:41.892/D: 📁 通过脚本路径推算项目根目录: /storage/emulated/0
05:55:41.893/D: 🖼️ 构建图片路径: 6.png → /storage/emulated/0/assets/算数游戏/6.png
05:55:41.893/D: ⚠️ 图片路径不存在: /storage/emulated/0/assets/算数游戏/6.png
0