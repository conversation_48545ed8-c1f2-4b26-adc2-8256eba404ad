/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用engines.myEngine()获取脚本目录，动态构建图片路径
 */

/**
 * 图片路径管理函数 - 单函数多接口
 * 通过engines.myEngine()获取当前脚本路径，推算项目根目录，构建图片绝对路径
 *
 * @param {string} 图片相对路径 - 相对于assets目录的图片路径
 * @param {string} 子目录 - 可选，指定assets下的子目录，如"算数游戏"、"google登陆"等
 * @returns {string} 图片的绝对路径
 */
function 图片路径(图片相对路径, 子目录) {
    try {
        // 获取当前脚本的完整路径
        var 当前脚本路径 = engines.myEngine().getSource().toString();
        console.log("🔍 当前脚本路径: " + 当前脚本路径);

        // 推算项目根目录
        var 项目根目录 = null;

        // 方法1：通过脚本路径推算（适用于脚本在项目内部运行）
        if (当前脚本路径.includes("/脚本/")) {
            项目根目录 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/脚本/"));
            console.log("📁 通过脚本路径推算项目根目录: " + 项目根目录);
        }
        // 方法2：通过magic关键字推算（适用于打包后的应用）
        else if (当前脚本路径.includes("/magic/")) {
            项目根目录 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/magic/") + 6);
            console.log("📁 通过magic关键字推算项目根目录: " + 项目根目录);
        }
        // 方法3：使用默认路径（兜底方案）
        else {
            项目根目录 = "/storage/emulated/0/脚本/magic";
            console.log("📁 使用默认项目根目录: " + 项目根目录);
        }

        // 构建assets目录路径
        var assets目录 = 项目根目录 + "/assets";

        // 构建完整的图片路径
        var 完整图片路径;

        if (子目录) {
            // 如果指定了子目录
            完整图片路径 = assets目录 + "/" + 子目录 + "/" + 图片相对路径;
        } else {
            // 如果没有指定子目录，直接拼接
            完整图片路径 = assets目录 + "/" + 图片相对路径;
        }

        // 路径标准化（处理多余的斜杠）
        完整图片路径 = 完整图片路径.replace(/\/+/g, "/");

        console.log("🖼️ 构建图片路径: " + 图片相对路径 + " → " + 完整图片路径);

        // 验证路径是否存在
        if (files.exists(完整图片路径)) {
            console.log("✅ 图片路径验证成功: " + 完整图片路径);
        } else {
            console.log("⚠️ 图片路径不存在: " + 完整图片路径);
        }

        return 完整图片路径;

    } catch (e) {
        console.error("❌ 图片路径构建失败:", e);

        // 错误时返回原始相对路径（兜底方案）
        var 兜底路径 = "/storage/emulated/0/脚本/magic/assets/" + (子目录 ? 子目录 + "/" : "") + 图片相对路径;
        console.log("🔄 使用兜底路径: " + 兜底路径);
        return 兜底路径;
    }
}

/**
 * 相对路径图片匹配方法 - 按照de.js的方法实现
 * 使用相对于当前脚本文件的路径来构建图片路径
 *
 * @param {string} 图片名 - 图片文件名，如"login.png"
 * @param {string} 子目录 - assets下的子目录，如"google"、"算数游戏"等
 * @returns {string} 相对路径格式的图片路径
 */
function 相对路径图片(图片名, 子目录) {
    try {
        // 按照de.js的方法：使用相对路径 "./assets/子目录/图片名"
        var 相对路径;

        if (子目录) {
            相对路径 = "./assets/" + 子目录 + "/" + 图片名;
        } else {
            相对路径 = "./assets/" + 图片名;
        }

        console.log("🔗 构建相对路径: " + 图片名 + " → " + 相对路径);

        return 相对路径;

    } catch (e) {
        console.error("❌ 相对路径构建失败:", e);

        // 错误时返回简单的相对路径
        var 兜底路径 = "./assets/" + (子目录 ? 子目录 + "/" : "") + 图片名;
        console.log("🔄 使用兜底相对路径: " + 兜底路径);
        return 兜底路径;
    }
}

/**
 * 智能图片路径方法 - 自动选择最佳路径方法
 * 优先使用相对路径方法，失败时回退到绝对路径方法
 *
 * @param {string} 图片名 - 图片文件名
 * @param {string} 子目录 - assets下的子目录
 * @param {string} 路径类型 - 可选，"相对"或"绝对"，默认为"相对"
 * @returns {string} 图片路径
 */
function 智能图片路径(图片名, 子目录, 路径类型) {
    try {
        路径类型 = 路径类型 || "相对";

        if (路径类型 === "相对") {
            console.log("📍 使用相对路径方法");
            return 相对路径图片(图片名, 子目录);
        } else {
            console.log("📍 使用绝对路径方法");
            return 图片路径(图片名, 子目录);
        }

    } catch (e) {
        console.error("❌ 智能路径选择失败:", e);

        // 错误时默认使用相对路径
        console.log("🔄 回退到相对路径方法");
        return 相对路径图片(图片名, 子目录);
    }
}

/**
 * 测试图片路径方法 - 用于验证不同路径方法的效果
 *
 * @param {string} 图片名 - 测试用的图片文件名
 * @param {string} 子目录 - 测试用的子目录
 */
function 测试图片路径(图片名, 子目录) {
    try {
        console.log("🧪 开始测试图片路径方法...");
        console.log("📝 测试参数: 图片名=" + 图片名 + ", 子目录=" + 子目录);

        // 测试绝对路径方法
        console.log("\n1️⃣ 测试绝对路径方法:");
        var 绝对路径结果 = 图片路径(图片名, 子目录);
        console.log("   结果: " + 绝对路径结果);

        // 测试相对路径方法
        console.log("\n2️⃣ 测试相对路径方法:");
        var 相对路径结果 = 相对路径图片(图片名, 子目录);
        console.log("   结果: " + 相对路径结果);

        // 测试智能路径方法
        console.log("\n3️⃣ 测试智能路径方法(相对):");
        var 智能相对结果 = 智能图片路径(图片名, 子目录, "相对");
        console.log("   结果: " + 智能相对结果);

        console.log("\n4️⃣ 测试智能路径方法(绝对):");
        var 智能绝对结果 = 智能图片路径(图片名, 子目录, "绝对");
        console.log("   结果: " + 智能绝对结果);

        console.log("\n✅ 图片路径方法测试完成");

        return {
            绝对路径: 绝对路径结果,
            相对路径: 相对路径结果,
            智能相对: 智能相对结果,
            智能绝对: 智能绝对结果
        };

    } catch (e) {
        console.error("❌ 测试图片路径方法失败:", e);
        return null;
    }
}


// 导出模块
module.exports = {
    图片路径: 图片路径,
    相对路径图片: 相对路径图片,
    智能图片路径: 智能图片路径,
    测试图片路径: 测试图片路径
};