/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用engines.myEngine()获取脚本目录，动态构建图片路径

 *
 * @param {string} 图片相对路径 - 相对于assets目录的图片路径
 * @param {string}
 * @returns {string} 图片的绝对路径
 */
function 图片路径(图片相对路径, 子目录) {
    try {
        // 获取当前脚本的完整路径
        var 当前脚本路径 = engines.myEngine().getSource().toString();
        // console.log("🔍 当前脚本路径: " + 当前脚本路径);

        // 推算项目根目录
        var 项目根目录 = null;

        // 通过magic关键字推算项目根目录（适用于APK打包后,开发过程中）
        // 当APK打包后，项目路径包含"/magic/"关键字
        // 通过此关键字可以准确定位项目根目录
        if (当前脚本路径.includes("/magic/")) {
            项目根目录 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/magic/") + 6);
            // console.log("📁 [方案二] 通过magic关键字推算项目根目录: " + 项目根目录);
        }

        // 构建assets目录路径
        var assets目录 = 项目根目录 + "/assets";

        // 构建完整的图片路径
        var 完整图片路径;

        if (子目录) {
            // 如果指定了子目录
            完整图片路径 = assets目录 + "/" + 子目录 + "/" + 图片相对路径;
        } else {
            // 如果没有指定子目录，直接拼接
            完整图片路径 = assets目录 + "/" + 图片相对路径;
        }

        // 路径标准化（处理多余的斜杠）
        完整图片路径 = 完整图片路径.replace(/\/+/g, "/");

        // console.log("🖼️ 构建图片路径: " + 图片相对路径 + " → " + 完整图片路径);

        // 验证路径是否存在
        if (files.exists(完整图片路径)) {
            // console.log("✅ 图片路径验证成功: " + 完整图片路径);
        } else {
            // console.log("⚠️ 图片路径不存在: " + 完整图片路径);
        }

        return 完整图片路径;

    } catch (e) {
        console.error("❌ 图片路径构建失败:", e);

        // 错误时返回原始相对路径（兜底方案）
        var 兜底路径 = "/storage/emulated/0/脚本/magic/assets/" + (子目录 ? 子目录 + "/" : "") + 图片相对路径;
        // console.log("🔄 使用兜底路径: " + 兜底路径);
        return 兜底路径;
    }
}



// 导出模块
module.exports = {
    图片路径: 图片路径
};