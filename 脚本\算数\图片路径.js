/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用engines.myEngine()获取脚本目录，动态构建图片路径
 */

/**
 * 图片路径管理函数 - 单函数多接口
 * 通过engines.myEngine()获取当前脚本路径，推算项目根目录，构建图片绝对路径
 *
 * @param {string} 图片相对路径 - 相对于assets目录的图片路径
 * @param {string} 子目录 - 可选，指定assets下的子目录，如"算数游戏"、"google登陆"等
 * @returns {string} 图片的绝对路径
 */
function 图片路径(图片相对路径, 子目录) {
    try {
        // 获取当前脚本的完整路径
        var 当前脚本路径 = engines.myEngine().getSource().toString();
        console.log("🔍 当前脚本路径: " + 当前脚本路径);

        // 推算项目根目录
        var 项目根目录 = null;

        // 方法1：通过脚本路径推算（适用于脚本在项目内部运行）
        if (当前脚本路径.includes("/脚本/")) {
            项目根目录 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/脚本/"));
            console.log("📁 通过脚本路径推算项目根目录: " + 项目根目录);
        }
        // 方法2：通过magic关键字推算（适用于打包后的应用）
        else if (当前脚本路径.includes("/magic/")) {
            项目根目录 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/magic/") + 6);
            console.log("📁 通过magic关键字推算项目根目录: " + 项目根目录);
        }
        // 方法3：使用默认路径（兜底方案）
        else {
            项目根目录 = "/storage/emulated/0/脚本/magic";
            console.log("📁 使用默认项目根目录: " + 项目根目录);
        }

        // 构建assets目录路径
        var assets目录 = 项目根目录 + "/assets";

        // 构建完整的图片路径
        var 完整图片路径;

        if (子目录) {
            // 如果指定了子目录
            完整图片路径 = assets目录 + "/" + 子目录 + "/" + 图片相对路径;
        } else {
            // 如果没有指定子目录，直接拼接
            完整图片路径 = assets目录 + "/" + 图片相对路径;
        }

        // 路径标准化（处理多余的斜杠）
        完整图片路径 = 完整图片路径.replace(/\/+/g, "/");

        console.log("🖼️ 构建图片路径: " + 图片相对路径 + " → " + 完整图片路径);

        // 验证路径是否存在
        if (files.exists(完整图片路径)) {
            console.log("✅ 图片路径验证成功: " + 完整图片路径);
        } else {
            console.log("⚠️ 图片路径不存在: " + 完整图片路径);
        }

        return 完整图片路径;

    } catch (e) {
        console.error("❌ 图片路径构建失败:", e);

        // 错误时返回原始相对路径（兜底方案）
        var 兜底路径 = "/storage/emulated/0/脚本/magic/assets/" + (子目录 ? 子目录 + "/" : "") + 图片相对路径;
        console.log("🔄 使用兜底路径: " + 兜底路径);
        return 兜底路径;
    }
}




/**
 * 广告模板图片路径快捷函数
 * @param {string} 图片名称 - 图片文件名
 * @param {string} 广告类型 - 广告类型，如"右广告"、"左广告"等
 * @returns {string} 完整的图片路径
 */
function 广告模板图片路径(图片名称, 广告类型) {
    if (广告类型) {
        return 算数游戏图片路径(广告类型 + "/" + 图片名称, "广告");
    } else {
        return 算数游戏图片路径(图片名称, "广告");
    }
}

/**
 * 答案数字图片路径快捷函数
 * @param {string} 数字 - 数字文件名，如"0.png"、"1.png"
 * @returns {string} 完整的图片路径
 */
function 答案数字图片路径(数字) {
    return 算数游戏图片路径(数字, "答案数字");
}

/**
 * Google登陆图片路径快捷函数
 * @param {string} 图片名称 - 图片文件名
 * @returns {string} 完整的图片路径
 */
function Google登陆图片路径(图片名称) {
    return 图片路径(图片名称, "google登陆");
}

/**
 * 获取项目根目录
 * @returns {string} 项目根目录路径
 */
function 获取项目根目录() {
    try {
        var 当前脚本路径 = engines.myEngine().getSource().toString();

        if (当前脚本路径.includes("/脚本/")) {
            return 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/脚本/"));
        } else if (当前脚本路径.includes("/magic/")) {
            return 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/magic/") + 6);
        } else {
            return "/storage/emulated/0/脚本/magic";
        }
    } catch (e) {
        console.error("❌ 获取项目根目录失败:", e);
        return "/storage/emulated/0/脚本/magic";
    }
}

/**
 * 检查图片路径是否存在
 * @param {string} 图片路径字符串 - 要检查的图片路径
 * @returns {boolean} 路径是否存在
 */
function 检查图片路径存在(图片路径字符串) {
    try {
        var 存在 = files.exists(图片路径字符串);
        if (存在) {
            console.log("✅ 图片路径存在: " + 图片路径字符串);
        } else {
            console.log("❌ 图片路径不存在: " + 图片路径字符串);
        }
        return 存在;
    } catch (e) {
        console.error("❌ 检查图片路径失败:", e);
        return false;
    }
}

// 导出模块
module.exports = {
    图片路径: 图片路径
};