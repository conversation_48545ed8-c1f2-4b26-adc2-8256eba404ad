/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用files.path()方法统一处理图片路径问题
 */

/**
 * 路径管理 - 单函数多接口统一路径处理
 * @param {string} 图片相对路径 - 相对于assets目录的图片路径
 * @param {string} 路径类型 - 可选参数，指定路径类型（默认为"算数游戏"）
 * @returns {string} 处理后的绝对路径
 */
function 路径管理(图片相对路径, 路径类型) {
    try {
        // 参数验证
        if (!图片相对路径 || typeof 图片相对路径 !== 'string') {
            console.error("❌ 路径管理：图片路径参数无效");
            return null;
        }

        var 完整相对路径;

        // 根据路径类型构建完整路径
        switch (路径类型) {
            case "新手教程":
                完整相对路径 = "assets/算数游戏/新手教程图片/" + 图片相对路径;
                break;
            case "广告模板":
                完整相对路径 = "assets/算数游戏/广告/" + 图片相对路径;
                break;
            case "答案数字":
                完整相对路径 = "assets/算数游戏/答案数字/" + 图片相对路径;
                break;
            case "google登陆":
                完整相对路径 = "assets/google登陆/" + 图片相对路径;
                break;
            case "算数游戏":
            default:
                // 默认算数游戏路径，支持多种子目录
                if (图片相对路径.startsWith("assets/")) {
                    // 如果已经包含assets前缀，直接使用
                    完整相对路径 = 图片相对路径;
                } else if (图片相对路径.includes("/")) {
                    // 如果包含子目录，添加assets前缀
                    完整相对路径 = "assets/" + 图片相对路径;
                } else {
                    // 单独文件名，默认放在算数游戏目录
                    完整相对路径 = "assets/算数游戏/" + 图片相对路径;
                }
                break;
        }

        // 使用files.path()转换为绝对路径
        var 绝对路径 = files.path(完整相对路径);
        
        // 路径验证
        if (!files.exists(绝对路径)) {
            console.warn("⚠️ 路径管理：图片文件不存在 - " + 绝对路径);
            // 不返回null，让调用方自己处理，可能是首次运行或文件确实不存在
        }

        console.log("🔗 路径转换: " + 图片相对路径 + " → " + 绝对路径);
        return 绝对路径;

    } catch (e) {
        console.error("❌ 路径管理异常:", e);
        console.error("输入参数 - 图片相对路径:", 图片相对路径, "路径类型:", 路径类型);
        return null;
    }
}

/**
 * 新手教程图片路径
 * @param {string} 图片名 - 图片文件名
 * @returns {string} 完整路径
 */
function 新手教程图片(图片名) {
    return 路径管理(图片名, "新手教程");
}

/**
 * 广告模板图片路径
 * @param {string} 子目录 - 广告子目录（如"右广告"）
 * @param {string} 图片名 - 图片文件名（可选）
 * @returns {string} 完整路径
 */
function 广告模板图片(子目录, 图片名) {
    if (图片名) {
        return 路径管理(子目录 + "/" + 图片名, "广告模板");
    } else {
        return 路径管理(子目录, "广告模板");
    }
}

/**
 * 答案数字图片路径
 * @param {string} 数字文件名 - 数字图片文件名
 * @returns {string} 完整路径
 */
function 答案数字图片(数字文件名) {
    return 路径管理(数字文件名, "答案数字");
}

/**
 * Google登陆图片路径
 * @param {string} 图片名 - 图片文件名
 * @returns {string} 完整路径
 */
function google登陆图片(图片名) {
    return 路径管理(图片名, "google登陆");
}

/**
 * 算数游戏通用图片路径
 * @param {string} 图片路径 - 可以是文件名或包含子目录的路径
 * @returns {string} 完整路径
 */
function 算数游戏图片(图片路径) {
    return 路径管理(图片路径, "算数游戏");
}

/**
 * 兼容旧版本的路径转换
 * @param {string} 旧路径 - 旧的相对路径格式（如"../../assets/..."）
 * @returns {string} 新的绝对路径
 */
function 转换旧路径(旧路径) {
    try {
        if (!旧路径 || typeof 旧路径 !== 'string') {
            return null;
        }

        // 移除旧的相对路径前缀
        var 清理路径 = 旧路径.replace(/^\.\.\/\.\.\//g, "");
        
        // 直接使用files.path()处理
        var 新路径 = files.path(清理路径);
        console.log("🔄 旧路径转换: " + 旧路径 + " → " + 新路径);
        return 新路径;

    } catch (e) {
        console.error("❌ 旧路径转换失败:", e);
        return null;
    }
}

/**
 * 批量路径检查
 * @param {Array} 路径列表 - 需要检查的路径数组
 * @returns {Object} 检查结果
 */
function 批量路径检查(路径列表) {
    var 检查结果 = {
        总数: 路径列表.length,
        存在: 0,
        不存在: 0,
        详情: []
    };

    for (var i = 0; i < 路径列表.length; i++) {
        var 路径 = 路径列表[i];
        var 绝对路径 = 路径管理(路径);
        var 存在 = files.exists(绝对路径);
        
        检查结果.详情.push({
            原路径: 路径,
            绝对路径: 绝对路径,
            存在: 存在
        });

        if (存在) {
            检查结果.存在++;
        } else {
            检查结果.不存在++;
        }
    }

    console.log("📊 批量路径检查结果:");
    console.log("  总数: " + 检查结果.总数);
    console.log("  存在: " + 检查结果.存在);
    console.log("  不存在: " + 检查结果.不存在);

    return 检查结果;
}

// 导出模块
module.exports = {
    路径管理: 路径管理,
    新手教程图片: 新手教程图片,
    广告模板图片: 广告模板图片,
    答案数字图片: 答案数字图片,
    google登陆图片: google登陆图片,
    算数游戏图片: 算数游戏图片,
    转换旧路径: 转换旧路径,
    批量路径检查: 批量路径检查
};
