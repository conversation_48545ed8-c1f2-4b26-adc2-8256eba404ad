/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用files.path()方法统一处理图片路径问题
 */

/**
 * 路径管理 - 单函数多接口统一路径处理
 * @param {string} 图片相对路径 - 图片路径，支持多种格式
 * @returns {string} 处理后的绝对路径
 */
function 路径管理(图片相对路径) {
    try {
        // 参数验证
        if (!图片相对路径 || typeof 图片相对路径 !== 'string') {
            console.error("❌ 路径管理：图片路径参数无效");
            return null;
        }

        var 完整相对路径;

        // 处理各种路径格式
        if (图片相对路径.startsWith("../../assets/")) {
            // 旧格式：../../assets/xxx
            完整相对路径 = 图片相对路径.replace("../../", "");
        } else if (图片相对路径.startsWith("assets/")) {
            // 标准格式：assets/xxx
            完整相对路径 = 图片相对路径;
        } else if (图片相对路径.startsWith("/")) {
            // 绝对路径格式：/xxx，移除开头的斜杠
            完整相对路径 = "assets" + 图片相对路径;
        } else {
            // 相对路径格式：xxx，自动添加assets前缀
            完整相对路径 = "assets/" + 图片相对路径;
        }

        // 标准化路径分隔符（统一使用正斜杠）
        完整相对路径 = 完整相对路径.replace(/\\/g, "/");

        // 使用files.path()转换为绝对路径
        var 绝对路径 = files.path(完整相对路径);

        // 验证文件是否存在（仅警告，不阻止返回）
        if (!files.exists(绝对路径)) {
            console.warn("⚠️ 路径管理：图片文件可能不存在 - " + 绝对路径);
        }

        console.log("🔗 路径转换: " + 图片相对路径 + " → " + 绝对路径);
        return 绝对路径;

    } catch (e) {
        console.error("❌ 路径管理异常:", e);
        console.error("输入路径:", 图片相对路径);

        // 异常情况下的备用处理
        try {
            var 备用路径 = files.path("assets/" + 图片相对路径);
            console.log("🔄 使用备用路径:", 备用路径);
            return 备用路径;
        } catch (e2) {
            console.error("❌ 备用路径也失败:", e2);
            return null;
        }
    }
}

// 导出模块
module.exports = {
    路径管理: 路径管理
};

