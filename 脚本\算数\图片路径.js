/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用files.path()方法统一处理图片路径问题
 */

/**
 * 路径管理 - 单函数多接口统一路径处理
 * @param {string} 图片相对路径 - 相对于assets目录的图片路径
 * @returns {string} 处理后的绝对路径
 */
function 路径管理(图片相对路径) {
    try {
        // 参数验证
        if (!图片相对路径 || typeof 图片相对路径 !== 'string') {
            console.error("❌ 路径管理：图片路径参数无效");
            return null;
        }

        var 完整相对路径;

        // 处理旧的相对路径格式
        if (图片相对路径.startsWith("../../assets/")) {
            完整相对路径 = 图片相对路径.replace("../../", "");
        } else if (图片相对路径.startsWith("assets/")) {
            完整相对路径 = 图片相对路径;
        } else {
            // 默认添加assets前缀
            完整相对路径 = "assets/" + 图片相对路径;
        }

        // 使用files.path()转换为绝对路径
        var 绝对路径 = files.path(完整相对路径);

        console.log("🔗 路径转换: " + 图片相对路径 + " → " + 绝对路径);
        return 绝对路径;

    } catch (e) {
        console.error("❌ 路径管理异常:", e);
        return null;
    }
}

// 导出模块
module.exports = {
    路径管理: 路径管理
};

