/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用engines.myEngine()获取脚本目录，动态构建图片路径
 *
 * 路径方案说明：
 * 方案一：APK打包后使用 - 通过脚本路径推算项目根目录
 * 方案二：APK打包后使用 - 通过magic关键字推算项目根目录
 * 方案三：开发环境使用 - 使用固定的模拟器绝对路径（/storage/emulated/0/脚本/magic）
 *
 * 注意：只有方案三适用于开发过程，方案一和方案二需要APK打包后才能正确工作
 */

/**
 * 图片路径管理函数 - 单函数多接口
 * 通过engines.myEngine()获取当前脚本路径，推算项目根目录，构建图片绝对路径
 *
 * 使用场景：
 * - 开发环境：使用方案三的模拟器绝对路径 /storage/emulated/0/脚本/magic
 * - 生产环境：APK打包后使用方案一或方案二的动态路径推算
 *
 * @param {string} 图片相对路径 - 相对于assets目录的图片路径
 * @param {string} 子目录 - 可选，指定assets下的子目录，如"算数游戏"、"google登陆"等
 * @returns {string} 图片的绝对路径
 */
function 图片路径(图片相对路径, 子目录) {
    try {
        // 获取当前脚本的完整路径
        var 当前脚本路径 = engines.myEngine().getSource().toString();
        console.log("🔍 当前脚本路径: " + 当前脚本路径);

        // 定义所有可能的项目根目录方案
        var 项目根目录候选列表 = [];

        // 方案一：通过脚本路径推算（适用于APK打包后运行）
        if (当前脚本路径.includes("/脚本/")) {
            var 方案一路径 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/脚本/"));
            项目根目录候选列表.push({
                路径: 方案一路径,
                描述: "[方案一-APK打包] 通过脚本路径推算",
                优先级: 1
            });
        }

        // 方案二：通过magic关键字推算（适用于APK打包后的特殊情况）
        if (当前脚本路径.includes("/magic/")) {
            var 方案二路径 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/magic/") + 6);
            项目根目录候选列表.push({
                路径: 方案二路径,
                描述: "[方案二-APK打包] 通过magic关键字推算",
                优先级: 2
            });
        }

        // 方案三：开发环境绝对路径（适用于开发过程中使用）
        项目根目录候选列表.push({
            路径: "/storage/emulated/0/脚本/magic",
            描述: "[方案三-开发环境] 使用模拟器绝对路径",
            优先级: 3
        });

        // 方案四：常见的其他可能路径（兜底方案）
        项目根目录候选列表.push({
            路径: "/storage/emulated/0/magic",
            描述: "[方案四-兜底] 简化路径",
            优先级: 4
        });

        项目根目录候选列表.push({
            路径: "/sdcard/脚本/magic",
            描述: "[方案五-兜底] sdcard路径",
            优先级: 5
        });

        console.log("� 共找到 " + 项目根目录候选列表.length + " 个项目根目录候选方案");

        // 多方案匹配：逐个尝试每个候选路径，直到找到存在的图片文件
        var 成功的方案 = null;
        var 最终图片路径 = null;

        for (var i = 0; i < 项目根目录候选列表.length; i++) {
            var 候选方案 = 项目根目录候选列表[i];
            console.log("🔄 尝试方案 " + (i + 1) + ": " + 候选方案.描述);
            console.log("   路径: " + 候选方案.路径);

            // 构建完整的图片路径
            var assets目录 = 候选方案.路径 + "/assets";
            var 完整图片路径;

            if (子目录) {
                完整图片路径 = assets目录 + "/" + 子目录 + "/" + 图片相对路径;
            } else {
                完整图片路径 = assets目录 + "/" + 图片相对路径;
            }

            console.log("   🖼️ 测试图片路径: " + 完整图片路径);

            // 检查图片文件是否存在
            if (files.exists(完整图片路径)) {
                console.log("   ✅ 图片文件存在！");
                成功的方案 = 候选方案;
                最终图片路径 = 完整图片路径;
                break;
            } else {
                console.log("   ❌ 图片文件不存在");
            }
        }

        // 检查是否找到有效的图片路径
        if (成功的方案) {
            console.log("🎉 多方案匹配成功！");
            console.log("📁 使用方案: " + 成功的方案.描述);
            console.log("🖼️ 最终图片路径: " + 最终图片路径);
            return 最终图片路径;
        } else {
            console.error("❌ 所有方案都失败，无法找到图片文件");
            console.error("🔍 查找的图片: " + 图片相对路径);
            console.error("📂 子目录: " + (子目录 || "无"));

            // 返回第一个方案的路径作为兜底（用于调试）
            var 兜底路径;
            if (项目根目录候选列表.length > 0) {
                var 兜底方案 = 项目根目录候选列表[0];
                var 兜底assets目录 = 兜底方案.路径 + "/assets";
                if (子目录) {
                    兜底路径 = 兜底assets目录 + "/" + 子目录 + "/" + 图片相对路径;
                } else {
                    兜底路径 = 兜底assets目录 + "/" + 图片相对路径;
                }
                console.log("🔧 返回兜底路径: " + 兜底路径);
                return 兜底路径;
            } else {
                throw new Error("无法构建图片路径：没有可用的候选方案");
            }
        }

        // 构建完整的图片路径
        var 完整图片路径;

        if (子目录) {
            // 如果指定了子目录
            完整图片路径 = assets目录 + "/" + 子目录 + "/" + 图片相对路径;
        } else {
            // 如果没有指定子目录，直接拼接
            完整图片路径 = assets目录 + "/" + 图片相对路径;
        }

        // 路径标准化（处理多余的斜杠）
        完整图片路径 = 完整图片路径.replace(/\/+/g, "/");

        console.log("🖼️ 构建图片路径: " + 图片相对路径 + " → " + 完整图片路径);

        // 验证路径是否存在
        if (files.exists(完整图片路径)) {
            console.log("✅ 图片路径验证成功: " + 完整图片路径);
        } else {
            console.log("⚠️ 图片路径不存在: " + 完整图片路径);
        }

        return 完整图片路径;

    } catch (e) {
        console.error("❌ 图片路径构建失败:", e);

        // 错误时返回原始相对路径（兜底方案）
        var 兜底路径 = "/storage/emulated/0/脚本/magic/assets/" + (子目录 ? 子目录 + "/" : "") + 图片相对路径;
        console.log("🔄 使用兜底路径: " + 兜底路径);
        return 兜底路径;
    }
}


/**
 * ==================== 路径方案详细说明 ====================
 *
 * 【方案一】APK打包后使用 - 脚本路径推算
 * 适用场景：脚本被打包成APK后运行
 * 工作原理：通过engines.myEngine()获取当前脚本路径，查找"/脚本/"关键字进行路径推算
 * 示例路径：/data/data/com.example.app/files/脚本/magic/脚本/算数/图片路径.js
 * 推算结果：/data/data/com.example.app/files/脚本/magic
 *
 * 【方案二】APK打包后使用 - magic关键字推算
 * 适用场景：APK打包后的特殊配置情况
 * 工作原理：通过engines.myEngine()获取当前脚本路径，查找"/magic/"关键字进行路径推算
 * 示例路径：/storage/emulated/0/Android/data/com.example.app/files/magic/脚本/算数/图片路径.js
 * 推算结果：/storage/emulated/0/Android/data/com.example.app/files/magic
 *
 * 【方案三】开发环境使用 - 模拟器绝对路径
 * 适用场景：开发过程中使用VSCode + AutoXjs插件连接模拟器进行调试
 * 工作原理：使用固定的绝对路径，直接指向模拟器中的项目目录
 * 模拟器路径：/storage/emulated/0/脚本/magic （Android模拟器环境）
 * 图片路径：/storage/emulated/0/脚本/magic/assets/算数游戏/图片名.png
 *
 * ==================== 使用注意事项 ====================
 *
 * 1. 开发环境调试：
 *    - 确保方案三的路径 "/storage/emulated/0/脚本/magic" 与模拟器中的实际项目路径一致
 *    - 如果项目在模拟器的其他目录，需要修改方案三的路径
 *
 * 2. APK打包发布：
 *    - 方案一和方案二会自动生效，无需修改代码
 *    - 确保assets目录和图片文件被正确打包到APK中
 *
 * 3. 路径格式：
 *    - 统一使用正斜杠 "/" 作为路径分隔符
 *    - 避免使用反斜杠 "\" 以确保跨平台兼容性
 *
 * 4. 图片资源管理：
 *    - 所有图片文件应放在 assets/ 目录下
 *    - 建议按功能模块创建子目录，如 assets/算数游戏/、assets/google登陆/ 等
 */

// 导出模块
module.exports = {
    图片路径: 图片路径
};