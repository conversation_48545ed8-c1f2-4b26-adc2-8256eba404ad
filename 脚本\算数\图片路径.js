/**
 * Magic项目图片路径管理模块
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 使用engines.myEngine()获取脚本目录，动态构建图片路径
 *
 * 路径方案说明：
 * 方案一：APK打包后使用 - 通过脚本路径推算项目根目录
 * 方案二：APK打包后使用 - 通过magic关键字推算项目根目录
 * 方案三：开发环境使用 - 使用固定的模拟器绝对路径（/storage/emulated/0/脚本/magic）
 *
 * 注意：只有方案三适用于开发过程，方案一和方案二需要APK打包后才能正确工作
 */

/**
 * 图片路径管理函数 - 单函数多接口
 * 通过engines.myEngine()获取当前脚本路径，推算项目根目录，构建图片绝对路径
 *
 * 使用场景：
 * - 开发环境：使用方案三的模拟器绝对路径 /storage/emulated/0/脚本/magic
 * - 生产环境：APK打包后使用方案一或方案二的动态路径推算
 *
 * @param {string} 图片相对路径 - 相对于assets目录的图片路径
 * @param {string} 子目录 - 可选，指定assets下的子目录，如"算数游戏"、"google登陆"等
 * @returns {string} 图片的绝对路径
 */
function 图片路径(图片相对路径, 子目录) {
    try {
        // 获取当前脚本的完整路径
        var 当前脚本路径 = engines.myEngine().getSource().toString();
        console.log("🔍 当前脚本路径: " + 当前脚本路径);

        // 推算项目根目录
        var 项目根目录 = null;

        // 方案一：通过脚本路径推算（适用于APK打包后运行）
        // 当脚本被打包成APK后，脚本路径通常包含"/脚本/"目录结构
        // 这种方式可以动态获取项目根目录，适用于打包后的应用
        if (当前脚本路径.includes("/脚本/")) {
            项目根目录 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/脚本/"));
            console.log("📁 [方案一-APK打包] 通过脚本路径推算项目根目录: " + 项目根目录);
        }
        // 方案二：通过magic关键字推算（适用于APK打包后的特殊情况）
        // 当APK打包后，项目路径可能包含"/magic/"关键字
        // 这是针对特定打包配置的兜底方案
        else if (当前脚本路径.includes("/magic/")) {
            项目根目录 = 当前脚本路径.substring(0, 当前脚本路径.lastIndexOf("/magic/") + 6);
            console.log("📁 [方案二-APK打包] 通过magic关键字推算项目根目录: " + 项目根目录);
        }
        // 方案三：开发环境绝对路径（适用于开发过程中使用）
        // 在开发过程中，使用固定的绝对路径可以确保图片资源的正确访问
        // 注意：这个路径是Android模拟器/设备的绝对路径，不是Windows电脑路径
        // 模拟器环境路径：/storage/emulated/0/脚本/magic （Android模拟器环境）
        else {
            项目根目录 = "/storage/emulated/0/脚本/magic";  // 模拟器绝对路径
            console.log("📁 [方案三-开发环境] 使用模拟器绝对路径: " + 项目根目录);
        }

        // 构建assets目录路径
        var assets目录 = 项目根目录 + "/assets";

        // 构建完整的图片路径
        var 完整图片路径;

        if (子目录) {
            // 如果指定了子目录
            完整图片路径 = assets目录 + "/" + 子目录 + "/" + 图片相对路径;
        } else {
            // 如果没有指定子目录，直接拼接
            完整图片路径 = assets目录 + "/" + 图片相对路径;
        }

        // 路径标准化（处理多余的斜杠）
        完整图片路径 = 完整图片路径.replace(/\/+/g, "/");

        console.log("🖼️ 构建图片路径: " + 图片相对路径 + " → " + 完整图片路径);

        // 验证路径是否存在
        if (files.exists(完整图片路径)) {
            console.log("✅ 图片路径验证成功: " + 完整图片路径);
        } else {
            console.log("⚠️ 图片路径不存在: " + 完整图片路径);
        }

        return 完整图片路径;

    } catch (e) {
        console.error("❌ 图片路径构建失败:", e);

        // 错误时返回原始相对路径（兜底方案）
        var 兜底路径 = "/storage/emulated/0/脚本/magic/assets/" + (子目录 ? 子目录 + "/" : "") + 图片相对路径;
        console.log("🔄 使用兜底路径: " + 兜底路径);
        return 兜底路径;
    }
}


/**
 * ==================== 路径方案详细说明 ====================
 *
 * 【方案一】APK打包后使用 - 脚本路径推算
 * 适用场景：脚本被打包成APK后运行
 * 工作原理：通过engines.myEngine()获取当前脚本路径，查找"/脚本/"关键字进行路径推算
 * 示例路径：/data/data/com.example.app/files/脚本/magic/脚本/算数/图片路径.js
 * 推算结果：/data/data/com.example.app/files/脚本/magic
 *
 * 【方案二】APK打包后使用 - magic关键字推算
 * 适用场景：APK打包后的特殊配置情况
 * 工作原理：通过engines.myEngine()获取当前脚本路径，查找"/magic/"关键字进行路径推算
 * 示例路径：/storage/emulated/0/Android/data/com.example.app/files/magic/脚本/算数/图片路径.js
 * 推算结果：/storage/emulated/0/Android/data/com.example.app/files/magic
 *
 * 【方案三】开发环境使用 - 模拟器绝对路径
 * 适用场景：开发过程中使用VSCode + AutoXjs插件连接模拟器进行调试
 * 工作原理：使用固定的绝对路径，直接指向模拟器中的项目目录
 * 模拟器路径：/storage/emulated/0/脚本/magic （Android模拟器环境）
 * 图片路径：/storage/emulated/0/脚本/magic/assets/算数游戏/图片名.png
 *
 * ==================== 使用注意事项 ====================
 *
 * 1. 开发环境调试：
 *    - 确保方案三的路径 "/storage/emulated/0/脚本/magic" 与模拟器中的实际项目路径一致
 *    - 如果项目在模拟器的其他目录，需要修改方案三的路径
 *
 * 2. APK打包发布：
 *    - 方案一和方案二会自动生效，无需修改代码
 *    - 确保assets目录和图片文件被正确打包到APK中
 *
 * 3. 路径格式：
 *    - 统一使用正斜杠 "/" 作为路径分隔符
 *    - 避免使用反斜杠 "\" 以确保跨平台兼容性
 *
 * 4. 图片资源管理：
 *    - 所有图片文件应放在 assets/ 目录下
 *    - 建议按功能模块创建子目录，如 assets/算数游戏/、assets/google登陆/ 等
 */

// 导出模块
module.exports = {
    图片路径: 图片路径
};