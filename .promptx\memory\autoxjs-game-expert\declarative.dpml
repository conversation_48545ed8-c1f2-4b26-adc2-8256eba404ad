<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753883208057_q13xcedjl" time="2025/07/30 21:46">
    <content>
      Magic项目当前状态和结构：
      1. 项目基于AutoXjs ozobiozobi v6.5.8.17，采用Android原生XML布局
      2. 主要目录结构：main.js入口，ui/界面模块，脚本/逻辑模块，assets/资源，存储数据/配置
      3. 核心模块：主页、日志页、脚本配置页、菜单抽屉页，每个模块包含UI定义和业务逻辑
      4. 已完成基础架构：应用启动、权限检查、配置管理、日志系统、页面切换
      5. 技术规范：ES5语法，4空格缩进，中文注释，禁用WebView/HTML/CSS
      6. 已修复的关键问题：XML格式错误(逗号改空格)，API兼容性(ui.layout替代setContentView)，控件安全访问
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753883223972_48kmf3vs4" time="2025/07/30 21:47">
    <content>
      开发规范和习惯：
      1. 技术准确性第一：所有代码基于AutoXjs官方文档，严禁虚假信息，不确定时联网搜索确认
      2. 问题导向开发：专注解决实际问题，绝对禁止创建示例/测试/demo文件，开发前完整阅读相关代码
      3. 简洁代码规则：用最少代码解决最多问题，函数命名动词_名词格式，参数全部可调，功能整合，直线逻辑
      4. 安全编程模式：控件访问前检查存在性，完善try-catch异常处理，及时释放资源避免内存泄漏
      5. API使用规范：优先ui.layout()替代setContentView，使用attr()方法设置控件属性，XML属性用空格分隔不用逗号
      6. 模块化架构：UI定义与业务逻辑分离，统一导出&quot;布局&quot;属性，require/module.exports模块管理
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1753883235308_jkzsqtmp5" time="2025/07/30 21:47">
    <content>
      已修复的关键技术问题和解决方案：
      1. XML格式错误：padding/margin/stroke属性必须用空格分隔，不能用逗号，如&quot;8dp 4dp&quot;不是&quot;8dp,4dp&quot;
      2. API兼容性问题：ui.setContentView()不稳定，改用ui.layout()；控件方法如setText()改用attr()方法
      3. 模块导出不一致：统一使用&quot;布局&quot;作为XML布局导出属性名，避免属性名不匹配导致页面切换失败
      4. 控件访问安全性：直接访问控件可能空指针异常，必须先检查if(ui.控件名)再操作
      5. 权限检查优化：避免启动时触发权限请求导致崩溃，移除截图权限检查，延迟提示用户授权
      6. 全局错误处理：使用events.on(&quot;uncaughtException&quot;)监听未捕获异常，安全记录日志
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753883323047_wmwlovg0n" time="2025/07/30 21:48">
    <content>
      PromptX项目配置和资源体系：
      1. 项目已配置完整的PromptX专业资源体系，包含6个专业资源
      2. 角色资源：autoxjs-game-expert专业角色，提供AutoXjs游戏辅助开发专业能力
      3. 执行模式：autoxjs-development-workflow开发工作流程，simple-code-rules简洁代码规则
      4. 知识库：autoxjs-ozobiozobi-expertise技术知识，magic-project-standards项目标准
      5. 思维模式：autoxjs-expert-thinking专家思维，指导AI的专业思考方式
      6. 记忆系统：已建立专业记忆库，支持跨会话记忆保持和精确检索
      7. 项目路径：D:\magic，MCP实例：mcp-22460(cursor)，完全支持多项目隔离
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
  <item id="mem_1754208480629_90yfxizxd" time="2025/08/03 16:08">
    <content>
      Magic项目完整开发规范深度学习总结：
    
      ## 项目核心定位
      - 基于AutoXjs ozobiozobi v6.5.8.17的专业游戏辅助脚本应用
      - 支持Android 9+系统，雷电模拟器环境(540x960, DPI240)
      - 专家级开发工程师角色，10年+AutoXjs经验，产品设计思维
    
      ## 技术栈严格限制
      - JavaScript引擎：Rhino 1.7.13，强制ES5语法
      - UI技术：Android原生XML布局+AutoXjs内置组件
      - 严格禁止：WebView、HTML、CSS、前端框架、浏览器特性
      - 开发工具：VSCode+AutoXjs Extension，Git版本控制
    
      ## 核心开发原则(4大原则)
      1. 技术准确性第一：基于官方文档，99%准确率，禁止虚假信息
      2. 自然语言优先：易读易懂的自然语言描述，避免过度结构化
      3. 问题导向开发：解决实际问题，绝对禁止创建示例/测试/demo文件
      4. 质量保证优先：可读性、可维护性、性能优化，系统性分析
    
      ## 编码规范标准
      - 语法：ES5标准，4空格缩进，语句结尾加分号，中文注释
      - 命名：中文变量名+英文API，动词_名词格式，语义清晰
      - 质量：代码覆盖率≥80%，关键函数100%注释，完善try-catch
      - 性能：内存≤500MB，响应≤500ms，及时释放资源
    
      ## AutoXjs v6.5.8.17新增功能掌握
      - 网络检测：networkUtils.isWifiAvailable()、getWifiIPv4()
      - 屏幕信息：device.getCurWidth()、getCurHeight()、getCurOrientation()
      - 调试增强：traceLog()跟踪堆栈，比console.log更强大
      - 截图增强：images.captureScreen(true)强制返回新对象
      - UI控件：setTint()设置色调，setBackgroundGradient()渐变背景
      - 布局分析：text(&#x27;文本&#x27;).find(true)带刷新查找，提高成功率
    
      ## API使用规范(关键变更)
      - 页面布局：ui.layout(布局对象) 替代 ui.setContentView()
      - 控件操作：ui.控件名.attr(&quot;属性&quot;, 值) 替代直接方法调用
      - XML格式：属性用空格分隔&quot;8dp 4dp&quot;，不用逗号&quot;8dp,4dp&quot;
      - 安全访问：if(ui.控件名)检查存在性再操作
      - 模块导出：统一使用&quot;布局&quot;作为XML导出属性名
    
      ## 专家级问题解决流程(4阶段)
      1. 问题理解与上下文建立：深度分析、技术栈聚焦、影响范围确定
      2. 多层次诊断：项目内部+架构层面+技术文档对比+外部资源检索
      3. 双方案制定：方案A(标准实现)+方案B(替代方案)
      4. 实施与优化：精确实施、反思性改进、预防机制建立
    
      ## AI开发助手严格规则
      - 需求理解：禁止立即编码，3-5轮确认，需求拆解，影响分析
      - 代码检查：全面审查相关文件，依赖关系分析，重复代码识别
      - 方案设计：详细实现方案，文件修改清单，质量保证方案
      - 执行流程：需求确认→代码检查→方案设计→用户确认→执行开发
      - 强制工具：必须使用interactive_feedback工具确认关键决策
    
      ## 简洁代码编写规则(7大原则)
      1. 函数命名：动词_名词格式，≤5个中文字，语义清晰
      2. 参数设计：全部可调，不硬编码，合理默认值，路径灵活
      3. 功能整合：一个函数多用途，参数决定行为，避免过度拆分
      4. 代码结构：直线逻辑，最少抽象，核心逻辑集中
      5. 注释原则：极简注释，参数自解释，删除冗余
      6. 灵活性：时间可调，行为可选，路径不限，返回有用信息
      7. 实用性：解决实际问题，功能完整，易于理解，性能考虑
    
      ## 项目管理标准
      - 文件组织：main.js入口，ui/界面，scripts/逻辑，assets/资源
      - 开发流程：需求分析→设计→开发→测试→部署
      - 文档管理：开发文档、用户文档、技术文档同步更新
      - 版本控制：Git分支策略，feat/fix/docs提交规范
    </content>
    <tags>#最佳实践 #流程管理 #工具使用</tags>
  </item>
  <item id="mem_1754208620792_nwcpe8xp2" time="2025/08/03 16:10">
    <content>
      README.md项目规则深度学习与遵守承诺：
    
      ## 学习完成确认
      已通过promptx_learn指令深度学习了以下核心资源：
      1. @knowledge://magic-project-standards - Magic项目标准
      2. @knowledge://autoxjs-ozobiozobi-expertise - AutoXjs技术专业知识
      3. @execution://autoxjs-development-workflow - AutoXjs开发工作流程
      4. @execution://simple-code-rules - 简洁代码编写规则
    
      ## 严格遵守承诺
      我承诺在Magic项目的所有开发过程中严格遵守以下规则：
    
      ### 技术约束严格遵守
      - JavaScript ES5语法：绝不使用ES6+特性，确保Rhino 1.7.13兼容
      - UI技术限制：严禁WebView、HTML、CSS、前端框架，只用Android原生XML
      - API版本约束：所有代码基于AutoXjs ozobiozobi v6.5.8.17官方文档
      - 性能约束：内存≤500MB，响应≤500ms，支持Android 9+
    
      ### 强制性开发规则遵守
      - 技术准确性：严禁虚假信息，不确定时联网搜索官方文档确认
      - 问题导向：绝对禁止创建示例、测试、demo等非功能性文件
      - 代码规范：4空格缩进、分号结尾、中文注释、中文变量名
      - 安全编程：控件访问前检查存在性、完善try-catch异常处理
      - 资源管理：及时释放图像资源、清理全局变量、避免内存泄漏
    
      ### 开发工作流程严格执行
      - 第一阶段：任务理解和分析（深度需求分析+预检查准备）
      - 第二阶段：技术方案设计（架构设计+开发顺序规划）
      - 第三阶段：代码实现和测试（编码实现+质量保证）
      - 第四阶段：问题解决方法（深度分析+系统诊断+双方案制定）
    
      ### 简洁代码规则强制执行
      - 核心原则：用最少代码，最灵活参数，最简洁命名，解决最多问题
      - 函数命名：动词_名词格式，≤5个中文字，语义清晰直观
      - 参数设计：全部可调，不硬编码，提供合理默认值
      - 功能整合：一个函数多用途，通过参数控制不同行为
      - 注释极简：只在函数开头用一行说明用法，避免冗余解释
    
      ### API使用规范严格遵循
      - 页面布局：ui.layout(布局对象) 替代 ui.setContentView()
      - 控件操作：ui.控件名.attr(&quot;属性&quot;, 值) 替代直接方法调用
      - XML格式：属性用空格分隔&quot;8dp 4dp&quot;，不用逗号&quot;8dp,4dp&quot;
      - 安全访问：if(ui.控件名)检查存在性再操作
      - 模块导出：统一使用&quot;布局&quot;作为XML导出属性名
    
      ### 质量标准严格达成
      - 技术准确性：基于官方文档API使用率100%，技术信息准确率≥99%
      - 代码质量：代码规范遵循率100%，注释覆盖率≥80%，错误处理≥95%
      - 性能指标：内存≤500MB，响应≤500ms，Android 9+兼容性100%
      - 用户体验：界面操作直观性≥90%，功能学习成本≤30分钟
    
      ## 执行保证
      在每次开发任务中，我将：
      1. 严格按照4阶段开发流程执行
      2. 确保所有代码符合简洁代码7大原则
      3. 使用推荐的新API和XML布局规范
      4. 达到所有质量标准的量化指标
      5. 绝不违反任何技术约束和强制规则
    
      这是我对Magic项目开发规范的庄严承诺，将在所有开发工作中严格遵守执行。
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
  <item id="mem_1754208668052_njoejb2dh" time="2025/08/03 16:11">
    <content>
      README.md项目规则深度学习与严格遵守承诺：
    
      ## 已完成的学习内容
      通过promptx_learn指令深度学习了README.md中的完整项目规范：
      1. Magic项目核心定位：AutoXjs ozobiozobi v6.5.8.17专业游戏辅助脚本
      2. 技术栈严格限制：ES5语法+Android原生XML，禁止WebView/HTML/CSS
      3. 4大核心开发原则：技术准确性第一、自然语言优先、问题导向开发、质量保证优先
      4. AutoXjs v6.5.8.17新增功能：网络检测、屏幕信息、调试增强、截图增强等
      5. API使用规范：ui.layout()替代setContentView()，attr()方法设置属性
      6. 专家级问题解决流程：4阶段系统性解决方案
      7. 简洁代码编写规则：7大原则，动词_名词命名，参数全可调
      8. AI开发助手严格规则：需求确认→代码检查→方案设计→用户确认→执行
    
      ## 严格遵守承诺
      我郑重承诺在Magic项目的所有开发工作中严格遵守以下规则：
    
      ### 技术约束绝对遵守
      - JavaScript引擎：只使用ES5语法，确保Rhino 1.7.13完全兼容
      - UI技术：严格禁止WebView、HTML、CSS、前端框架，只用Android原生XML
      - API版本：所有代码必须基于AutoXjs ozobiozobi v6.5.8.17官方文档
      - 性能要求：内存使用≤500MB，响应时间≤500ms，支持Android 9+
    
      ### 开发原则严格执行
      - 技术准确性第一：99%准确率，禁止虚假信息，不确定时联网搜索确认
      - 问题导向开发：绝对禁止创建示例/测试/demo文件，只解决实际问题
      - 自然语言优先：易读易懂的描述，避免过度结构化
      - 质量保证优先：可读性、可维护性、性能优化系统性分析
    
      ### 编码规范强制遵循
      - 语法风格：4空格缩进，语句结尾加分号，中文注释，中文变量名
      - 命名规范：动词_名词格式，≤5个中文字，语义清晰直观
      - 安全编程：控件访问前检查存在性，完善try-catch异常处理
      - 资源管理：及时释放图像资源，清理全局变量，避免内存泄漏
    
      ### API使用规范严格遵守
      - 页面布局：ui.layout(布局对象) 替代 ui.setContentView()
      - 控件操作：ui.控件名.attr(&quot;属性&quot;, 值) 替代直接方法调用
      - XML格式：属性用空格分隔&quot;8dp 4dp&quot;，绝不用逗号&quot;8dp,4dp&quot;
      - 安全访问：if(ui.控件名)检查存在性再操作
      - 模块导出：统一使用&quot;布局&quot;作为XML导出属性名
    
      ### 简洁代码规则强制执行
      - 核心原则：用最少代码，最灵活参数，最简洁命名，解决最多问题
      - 函数命名：动词_名词格式，不超过5个中文字
      - 参数设计：全部可调，不硬编码，提供合理默认值
      - 功能整合：一个函数多用途，参数决定行为
      - 注释极简：只在函数开头用一行说明用法
    
      ### AI开发助手规则严格遵守
      - 需求理解：禁止立即编码，3-5轮确认，需求拆解，影响分析
      - 代码检查：全面审查相关文件，依赖关系分析，重复代码识别
      - 方案设计：详细实现方案，文件修改清单，质量保证方案
      - 执行流程：需求确认→代码检查→方案设计→用户确认→执行开发
    
      ### 专家级问题解决流程严格执行
      - 第一阶段：问题理解与上下文建立
      - 第二阶段：多层次诊断与双方案制定
      - 第三阶段：假设验证与方案制定
      - 第四阶段：实施与持续优化
    
      ## 执行保证声明
      我承诺在每次开发任务中：
      1. 严格按照README.md中的所有规则执行
      2. 绝不违反任何技术约束和强制规则
      3. 确保代码质量达到所有量化标准
      4. 使用推荐的新API和规范实现方式
      5. 遵循专家级问题解决流程
    
      这是我对Magic项目开发规范的庄严承诺，将在所有开发工作中严格遵守执行。
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
  <item id="mem_1754208821334_ulieh7qm7" time="2025/08/03 16:13">
    <content>
      Magic项目README.md完整开发规范总结：
    
      ## 核心开发原则（必须严格遵守）
      1. **技术准确性第一**：所有代码基于AutoXjs官方文档，严禁虚假信息，不确定时联网搜索确认，确保99%准确率
      2. **问题导向开发**：专注解决实际问题，绝对禁止创建示例/测试/demo文件，开发前完整阅读相关代码
      3. **自然语言优先**：文档易读易懂，避免过度结构化
      4. **质量保证优先**：代码可读性、可维护性、性能优化，系统性分析和逐步推理
    
      ## 技术栈限制（严格执行）
      - ✅ 允许：AutoXjs ozobiozobi v6.5.8.17、Android原生XML布局、ES5语法、AutoXjs内置UI组件
      - ❌ 严禁：WebView、HTML/CSS/JS、前端框架、浏览器特性、ES6+语法
    
      ## 编码规范（强制要求）
      - ES5标准语法，4空格缩进，语句结尾加分号，中文注释
      - 中文变量名配合英文API，函数名中文动词+名词格式
      - 代码覆盖率≥80%，关键函数100%注释覆盖率
      - 内存使用≤500MB，响应时间≤500ms
    
      ## API使用规范（v6.5.8.17）
      - ✅ 推荐：ui.layout()替代setContentView，attr()方法设置控件属性，XML属性空格分隔
      - ❌ 避免：setText()、setVisibility()等直接方法，XML属性逗号分隔
      - 安全编程：控件访问前检查存在性if(ui.控件名)，完善try-catch异常处理
    
      ## 双方案制定规则（必须执行）
      针对每个问题必须提供两种可运行的正确解决方案，向开发者提问要使用哪种方案后，开发者提示&quot;确认&quot;后才能执行修改代码等编程任务：
      - 方案A：基于官方文档的标准实现方式，遵循项目现有架构
      - 方案B：不同技术路径的可行替代方案，考虑性能优化或特殊场景
    
      ## 简洁代码规则（强制执行）
      核心原则：用最少的代码，最灵活的参数，最简洁的命名，解决最多的实际问题
      - 函数命名：动词_名词格式，不超过5个中文字，语义清晰
      - 参数设计：全部可调，不硬编码，合理默认值，路径灵活
      - 功能整合：一个函数多用途，参数决定行为，避免过度拆分
      - 代码结构：直线逻辑，最少抽象，核心逻辑集中
      - 注释原则：极简注释，参数自解释，删除冗余
    
      ## AI开发助手严格规则（必须执行）
      1. 需求理解阶段：禁止立即编码，必须3-5轮提问确认，需求拆解，影响范围分析
      2. 代码检查阶段：必须检查所有相关文件，依赖关系分析，重复代码识别
      3. 方案设计阶段：详细实现方案，文件修改清单，质量保证方案
      4. 执行流程：需求接收→需求确认→代码检查→方案设计→用户确认→执行开发
      5. 必须等待用户明确&quot;确认执行&quot;指令后才开始编码
    
      ## 质量标准（严格要求）
      - 内存管理：及时释放资源，避免内存泄漏，事件监听器正确清理
      - 性能优化：多线程架构，异步处理，图像缓存压缩，HTTP池化
      - 安全性：控件存在性检查，函数存在性验证，完善异常处理
      - 兼容性：Android 9+系统，雷电模拟器540x960 DPI240环境
    </content>
    <tags>#最佳实践 #流程管理</tags>
  </item>
  <item id="mem_1754208872115_orbzujzl2" time="2025/08/03 16:14">
    <content>
      AutoXjs ozobiozobi v6.5.8.17 API规范和新增功能详解：
    
      ## 推荐使用的新API（必须遵循）
      ### 页面布局管理
      - ✅ ui.layout(布局对象) 替代 ui.setContentView()
      - ✅ ui.控件名.attr(&quot;属性名&quot;, 值) 替代直接方法调用
      - ✅ ui.控件名.on(&quot;事件名&quot;, 回调函数) 进行标准事件绑定
    
      ### 控件操作标准
      - ui.文本控件.attr(&quot;text&quot;, &quot;新文本内容&quot;)
      - ui.按钮控件.attr(&quot;visibility&quot;, &quot;visible/gone/invisible&quot;)
      - ui.开关控件.attr(&quot;checked&quot;, true)
      - ui.文本控件.attr(&quot;textColor&quot;, &quot;#FF0000&quot;)
      - ui.控件.attr(&quot;background&quot;, &quot;#00A843&quot;)
      - 安全访问：if (ui.控件名) { ui.控件名.attr(&quot;属性&quot;, &quot;值&quot;); }
    
      ## 避免使用的过时API（严格禁止）
      - ❌ ui.setContentView() → ✅ ui.layout()
      - ❌ 控件.setText() → ✅ 控件.attr(&quot;text&quot;, 值)
      - ❌ 控件.setVisibility() → ✅ 控件.attr(&quot;visibility&quot;, &quot;visible/gone/invisible&quot;)
      - ❌ 控件.setTextColor() → ✅ 控件.attr(&quot;textColor&quot;, 颜色值)
      - ❌ 控件.setChecked() → ✅ 控件.attr(&quot;checked&quot;, 布尔值)
    
      ## XML布局格式规范（强制要求）
      - ✅ 正确：padding=&quot;8dp 4dp&quot; margin=&quot;16dp 8dp&quot; stroke=&quot;1dp #00A843&quot;
      - ❌ 错误：padding=&quot;8dp,4dp&quot; margin=&quot;16dp,8dp&quot; stroke=&quot;1dp,#00A843&quot;
      - 模块导出：统一使用&quot;布局&quot;作为XML布局导出名
    
      ## 新增功能详解
      ### 网络检测功能
      - networkUtils.isWifiAvailable() - 检查WiFi可用性
      - networkUtils.getWifiIPv4() - 获取WiFi IP地址
      - networkUtils.getIPList() - 获取所有IP列表
    
      ### 屏幕信息实时获取
      - device.getCurWidth() / device.getCurHeight() - 获取当前屏幕宽高
      - getCurOrientation() - 获取屏幕方向(1=竖屏，2=横屏)
      - getStatusBarHeight() - 获取状态栏高度
    
      ### 调试增强
      - traceLog(&quot;调试信息&quot;) - 跟踪堆栈行号，比console.log更强大
      - traceLog(&quot;重要信息&quot;, true) - 输出到文件
    
      ### 截图增强
      - images.captureScreen(true) - 强制返回新对象，避免缓存问题
    
      ### UI控件增强
      - ui.input.setTint() / ui.checkbox.setTint() / ui.radio.setTint() - 设置控件色调
      - ui.button.setBackgroundGradient() - 设置按钮渐变背景
    
      ### 布局分析增强
      - text(&#x27;文本&#x27;).find(true) - 带刷新的查找控件，提高查找成功率
    
      ### 时间处理增强
      - dateFormat(时间戳, &quot;格式&quot;) - 格式化时间戳，默认&quot;yyyy-MM-dd HH:mm:ss.SSS&quot;
      - dateToTimestamp(&quot;时间字符串&quot;) - 时间字符串转时间戳
    
      ### 视图工具功能
      - viewUtils.findParentById(view, id) - 通过ID查找父视图
      - viewUtils.pxToSp() / viewUtils.dpToPx() / viewUtils.pxToDp() - 单位转换
    
      ## 安全编程模式（必须执行）
      ### 控件存在性检查
      function 安全更新控件() {
      if (ui.日志列表) { ui.日志列表.removeAllViews(); }
      if (ui.空状态区域) { ui.空状态区域.attr(&quot;visibility&quot;, &quot;visible&quot;); }
      if (ui.权限开关) { ui.权限开关.on(&quot;check&quot;, function(checked) { }); }
      }
    
      ### 页面切换标准模式
      function 切换到页面(页面名称) {
      try {
      var 页面模块 = require(&#x27;./UI&#x27; + 页面名称 + &#x27;页面.js&#x27;);
      ui.layout(页面模块.布局);
      var 逻辑模块 = require(&#x27;./&#x27; + 页面名称 + &#x27;逻辑.js&#x27;);
      if (逻辑模块.初始化逻辑) { 逻辑模块.初始化逻辑(); }
      } catch (e) {
      console.error(&quot;页面切换失败:&quot;, e);
      traceLog(&quot;页面切换错误详情: &quot; + e.toString());
      }
      }
    
      ## 官方文档地址（必须参考）
      - 项目地址: https://github.com/ozobiozobi/Autoxjs_v6_ozobi
      - 官方文档: https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/overview.html
      - UI界面文档: https://autox-doc.vercel.app/docs/rhino/base/ui
      - 完整文档: https://autox-doc.vercel.app/docs/rhino/documentation
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1754208909716_37jwt1i96" time="2025/08/03 16:15">
    <content>
      AI开发助手严格执行规则（README.md核心要求）：
    
      ## 需求理解阶段（必须执行）
      ### 多轮需求确认
      - ❌ 禁止立即开始编码：收到需求后必须先进行需求分析
      - ✅ 必须提问确认：通过3-5轮提问确保完全理解需求
      - ✅ 需求拆解：将复杂需求拆解为具体的技术实现点
      - ✅ 影响范围分析：分析修改对现有代码的影响范围
    
      ### 需求确认清单（开始开发前必须确认）
      - [ ] 功能的具体行为和预期结果
      - [ ] 涉及的文件和模块范围
      - [ ] 前端UI变化（如有）
      - [ ] 后端逻辑变化（如有）
      - [ ] 与现有功能的集成方式
      - [ ] 性能和安全性要求
    
      ## 代码检查阶段（必须执行）
      ### 全面代码审查
      - ✅ 必须检查所有相关文件：使用view工具查看所有涉及的文件
      - ✅ 依赖关系分析：检查模块间的依赖关系
      - ✅ 重复代码识别：查找可能的重复逻辑
      - ✅ 未使用代码识别：查找未使用的变量、函数、导入
    
      ### 架构一致性检查
      - ✅ 前后端逻辑一致性：确保前端UI与后端逻辑匹配
      - ✅ 模块职责清晰：每个模块职责单一且明确
      - ✅ 接口设计合理：模块间接口设计合理
      - ✅ 错误处理完整：所有可能的错误情况都有处理
    
      ## 方案设计阶段（必须执行）
      ### 技术方案设计
      - ✅ 详细实现方案：提供具体的实现步骤
      - ✅ 文件修改清单：列出需要修改的所有文件
      - ✅ 新增代码说明：说明新增代码的作用
      - ✅ 删除代码说明：说明删除代码的原因
    
      ### 质量保证方案
      - ✅ 内存管理：确保无内存泄漏风险
      - ✅ 性能优化：避免不必要的计算和资源占用
      - ✅ 安全性检查：防止全局变量污染、空指针等
      - ✅ 错误处理：完善的异常捕获和处理
    
      ## 开发执行规则（严格执行流程）
      ### 执行流程
      需求接收 → 需求确认(3-5轮) → 代码检查 → 方案设计 → 用户确认 → 执行开发
    
      ### 禁止行为
      - ❌ 禁止立即编码：未经需求确认不得开始编码
      - ❌ 禁止盲目修改：未检查相关文件不得修改代码
      - ❌ 禁止重复逻辑：不得创建重复的功能实现
      - ❌ 禁止破坏性修改：不得破坏现有功能
      - ❌ 禁止创建测试文件：用户明确禁止创建测试文件
    
      ### 必须行为
      - ✅ 必须检查文件内容：使用view工具检查相关文件
      - ✅ 必须分析依赖关系：使用codebase-retrieval分析代码关系
      - ✅ 必须提供详细方案：包含具体的实现步骤和文件清单
      - ✅ 必须等待用户确认：获得明确的&quot;确认执行&quot;指令后才开始编码
    
      ## 双方案制定规则（必须执行）
      针对每个问题必须提供两种可运行的正确解决方案，向开发者提问要使用哪种方案后，开发者提示&quot;确认&quot;后才能执行修改代码等编程任务：
      - **方案A**：基于官方文档的标准实现方式，遵循项目现有架构和设计模式
      - **方案B**：提供不同技术路径的可行替代方案，考虑性能优化或特殊场景需求
    
      ## 执行确认指令（必须等待）
      用户必须明确给出以下确认指令之一，AI才能开始执行：
      - &quot;确认执行&quot;
      - &quot;开始开发&quot;
      - &quot;按方案执行&quot;
      - &quot;同意实施&quot;
    
      ## 代码质量标准（严格要求）
      ### 内存管理
      - 事件监听器必须正确清理
      - 数组清空使用array.length = 0而非array = []
      - 全局变量必须在适当时机清理
      - 模块销毁时必须清理所有资源
    
      ### 安全性保证
      - 所有控件访问前必须检查存在性
      - 所有函数调用前必须检查函数存在性
      - 完善的try-catch异常处理
      - 避免全局变量污染
    
      ### 性能优化
      - 避免不必要的重复计算
      - 合理的模块加载策略
      - 高效的数据结构使用
      - 及时的资源释放
    
      **核心原则：未收到明确确认指令前，我不得开始任何代码修改操作。所有关键决策点都必须与用户确认。**
    </content>
    <tags>#最佳实践 #流程管理 #工具使用</tags>
  </item>
  <item id="mem_1754309976000_fh72msun8" time="2025/08/04 20:19">
    <content>
      Magic项目最新开发进度和成果总结：
    
      ## 核心功能模块完成情况
      1. **OCR识别引擎优化**：ocr看广告.js已实现五重识别体系（原图+二值化+反向二值化+轮廓检测+图片匹配），并添加了表达式识别失败保护机制，连续失败5次自动退出执行看广告程序
      2. **算数游戏自动化**：公式2.js实现完整的算数题识别和答案选择，包含表达式三区域识别、答案四区域识别、智能点击策略，已添加保护机制防止无限循环
      3. **游戏界面处理**：开始界面.js优化了启动流程，减少延时提升响应速度，su_main.js调整了随机等待时间和循环逻辑
      4. **符号识别系统**：建立了完整的符号区域定义体系，支持左上角、右上角、横屏右上角等多区域符号识别，新增&quot;右双箭头-3&quot;符号支持
    
      ## 技术架构优化成果
      1. **图片匹配符号免配置**：修改了是否为有效点击符号函数，图片匹配的符号不受区域符号列表限制，添加新模板图片时无需手动更新符号列表
      2. **轮廓检测功能集成**：成功集成OpenCV轮廓检测预处理，使用AutoXjs内置方法避免兼容性问题，提高边缘模糊符号的识别率
      3. **性能优化调整**：答案点击延时优化为150-200ms，循环间隔优化为150-200ms，权限等待时间减少到100ms，提升整体执行效率
      4. **错误处理增强**：添加了详细的错误分析和堆栈跟踪，使用traceLog进行调试，建立了完善的异常处理机制
    
      ## 代码质量提升
      1. **输出内容优化**：注释掉了详细的性能分析输出，只保留关键的总耗时信息，减少日志冗余
      2. **资源管理完善**：所有图像资源都有及时的recycle()调用，避免内存泄漏问题
      3. **参数调优**：根据实际使用效果调整了各种延时参数，平衡了速度和稳定性
      4. **模块化设计**：保持了清晰的模块分离，UI定义与业务逻辑分离，便于维护和扩展
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754309999451_71k6l5y6w" time="2025/08/04 20:19">
    <content>
      用户的编码习惯和偏好规则总结：
    
      ## 性能优化偏好
      1. **延时参数精细化**：用户倾向于将延时参数调整到最优值，如答案点击延时从250-320ms优化到150-200ms，循环间隔从250-300ms优化到150-200ms，权限等待从1000ms减少到100ms
      2. **响应速度优先**：在保证稳定性的前提下，优先提升程序执行速度，减少不必要的等待时间
      3. **参数可配置化**：所有延时和等待参数都要可调节，不硬编码固定值
    
      ## 输出内容管理偏好
      1. **日志简洁化**：倾向于注释掉详细的调试输出，只保留关键信息如总耗时，避免日志冗余
      2. **关键信息突出**：保留对用户有价值的信息，如执行结果、错误提示、性能数据
      3. **调试信息可控**：调试信息应该可以通过参数控制开启/关闭
    
      ## 功能扩展习惯
      1. **渐进式增强**：喜欢在现有功能基础上逐步添加新功能，如从四重识别扩展到五重识别
      2. **保护机制完善**：重视异常处理和保护机制，如表达式识别失败5次后自动退出
      3. **向后兼容**：新功能不影响现有功能的正常运行
    
      ## 代码组织偏好
      1. **模块化清晰**：每个功能模块职责明确，UI与逻辑分离
      2. **命名语义化**：使用中文命名，函数名直接表达功能意图
      3. **注释适度**：重要逻辑有注释说明，避免过度注释
    
      ## 用户体验关注点
      1. **执行效率**：关注程序执行速度和资源占用
      2. **稳定性**：重视程序的稳定运行和异常处理
      3. **可维护性**：代码结构清晰，便于后续修改和扩展
      4. **配置灵活性**：重要参数可配置，适应不同使用场景
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1754310031143_pyf8g94nk" time="2025/08/04 20:20">
    <content>
      Magic项目关键技术经验和解决方案总结：
    
      ## 关键技术突破
      1. **轮廓检测API兼容性问题**：发现images.matToImage()方法在ozobiozobi v6.5.8.17中存在兼容性问题，解决方案是使用AutoXjs内置的images.threshold()方法替代复杂的OpenCV Mat操作，既保证了功能实现又提高了兼容性
      2. **图片匹配符号限制问题**：通过修改是否为有效点击符号函数，添加图片匹配来源检查逻辑，使图片匹配的符号跳过符号列表验证，解决了添加新模板图片需要手动更新符号列表的问题
      3. **表达式识别失败循环问题**：实现了失败计数器保护机制，连续失败5次自动退出算数循环执行看广告程序，避免无限循环卡死
    
      ## OCR识别优化经验
      1. **五重识别体系**：原图识别+二值化识别+反向二值化识别+轮廓检测识别+图片模板匹配识别，每种方法针对不同的图像特征，大幅提高识别成功率
      2. **智能去重合并**：通过坐标距离计算避免重复识别相同位置的符号，优先保留置信度更高的结果
      3. **区域化识别策略**：将屏幕分为多个区域分别识别，提高识别精度和性能
    
      ## 性能优化实践
      1. **延时参数调优**：通过实际测试找到最优的延时参数，在保证稳定性的前提下最大化执行速度
      2. **资源管理优化**：所有图像资源及时recycle()释放，避免内存泄漏导致的性能下降
      3. **输出内容精简**：注释掉详细的调试输出，只保留关键信息，减少日志处理开销
    
      ## 错误处理最佳实践
      1. **坐标属性访问安全**：发现OCR结果对象不能直接添加自定义属性，解决方案是创建新的结果对象包含所需信息
      2. **控件存在性检查**：所有UI控件访问前都要检查存在性，使用if(ui.控件名)模式避免空指针异常
      3. **异常信息详细化**：使用traceLog()提供详细的错误堆栈信息，便于快速定位问题
    
      ## API使用经验总结
      1. **推荐API组合**：ui.layout()+attr()方法+安全检查模式，替代旧的setContentView()+直接方法调用
      2. **XML格式规范**：属性值必须用空格分隔，不能用逗号，这是常见的格式错误
      3. **模块导出统一**：使用&quot;布局&quot;作为统一的XML导出属性名，避免属性名不匹配问题
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1754311373662_ec80rizb4" time="2025/08/04 20:42">
    <content>
      Magic项目UI脚本页面清理完成：
      1. 成功删除操作配置卡片和广告配置卡片的完整UI界面
      2. 保留了游戏配置卡片、分数控制卡片、底部按钮和导航栏
      3. 采用方案B保守清理方案，注释掉相关UI操作代码但保留数据处理逻辑
      4. 删除了获取左键设置、获取右键设置、获取广告配置等不再需要的函数
      5. 删除了main.js中的测试全局日志系统函数
      6. 修改后的界面更简洁，只保留核心的游戏配置和分数控制功能
      7. 所有配置管理功能仍然完整，便于将来恢复UI界面
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754311563723_pwlaoahzi" time="2025/08/04 20:46">
    <content>
      修复main.js中测试全局日志系统函数调用错误：
      1. 问题：main.js第155行调用了已删除的测试全局日志系统()函数，导致ReferenceError
      2. 原因：删除函数定义时遗漏了删除函数调用，setTimeout中仍有对该函数的引用
      3. 解决方案：删除第153-156行的setTimeout调用代码
      4. 修复结果：应用启动不再出现ReferenceError错误
      5. 经验教训：删除函数时要全局搜索所有调用点，确保完全清理
      6. 验证方法：使用正则搜索确认没有其他调用残留
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754312292833_vzk9irgl9" time="2025/08/04 20:58">
    <content>
      脚本逻辑.js测试代码清理完成：
      1. 删除了方式1：直接使用全局日志系统的测试代码（原第133-145行）
      2. 删除了方式3：尝试重新初始化全局日志桥接器的测试代码（原第147-167行）
      3. 保留了方式2：尝试直接使用UI日志模块的代码，这是实际需要的功能
      4. 测试Google登录日志函数现在更简洁，只保留必要的UI日志模块测试
      5. 删除后的代码结构完整，没有语法错误
      6. 符合项目简洁代码规则，删除了不必要的测试代码
      7. 保持了核心功能的完整性，UI日志模块仍然可用
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1754312895454_tfy2v14mc" time="2025/08/04 21:08">
    <content>
      配置管理.js无用配置清理完成（方案A）：
      1. 完全删除操作配置：左键设置、右键设置、原点设置、障碍检测（原第21-47行）
      2. 完全删除广告配置：自动看广告、左侧区域、右侧区域配置（原第30-47行）
      3. 更新游戏配置项名称：自动启动游戏→登陆play商店，自动玩游戏→首次帐号注册
      4. 完善配置验证逻辑：更新验证函数以匹配新的配置项名称，增加所有游戏配置项的验证
      5. 配置结构简化：从5个配置分组减少到3个（游戏配置、分数控制、应用设置）
      6. 存储空间优化：删除无用配置减少了存储占用
      7. 代码维护性提升：配置结构更清晰，验证逻辑更完整
      8. 与UI界面保持一致：配置项与实际UI界面完全匹配
    </content>
    <tags>#其他</tags>
  </item>
</memory>