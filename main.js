"ui";

/**
 * Magic 游戏辅助脚本 - 应用主入口
 * 基于AutoXjs原生UI组件开发
 * 使用统一的#00A843绿色主题
 *
 * 设计原则：
 * - main.js作为应用启动入口，逻辑功能在其他文件中实现
 * - 模块化设计，职责分离
 * - 统一的错误处理和日志记录
 */

// 导入核心模块
var 简洁日志管理器 = require("./简洁日志管理器.js");
var 配置管理 = require("./存储数据/配置管理.js");
var 主页界面 = require("./ui/主页/UI主页页面.js");
var 主页逻辑 = require("./ui/主页/主页逻辑.js");
var 日志逻辑 = require("./ui/日志页/日志逻辑.js");
var 抽屉页逻辑 = require("./ui/菜单抽屉页/侧滑抽屉.js");

// 应用状态管理
var 应用状态 = {
    已初始化: false,
    当前页面: "主页",
    启动时间: null
};

/**
 * 安全日志记录函数 - 在应用退出时也能安全使用
 * @param {String} 内容 - 日志内容
 * @param {String} 类型 - 日志类型
 */
function 安全记录日志(内容, 类型) {
    try {
        // 总是记录到控制台
        switch (类型) {
            case "成功":
                console.log("[成功]", 内容);
                break;
            case "警告":
                console.warn("[警告]", 内容);
                break;
            case "错误":
                console.error("[错误]", 内容);
                break;
            default:
                console.log("[信息]", 内容);
        }

        // 尝试使用全局日志系统
        if (typeof global !== 'undefined' && global.日志系统) {
            try {
                global.日志系统.添加日志("[系统] " + 内容, 类型);
            } catch (globalError) {
                console.warn("全局日志系统输出失败:", globalError.message);
            }
        }

        // 备用：直接使用UI日志逻辑
        else if (ui && 日志逻辑 && 日志逻辑.添加日志) {
            try {
                日志逻辑.添加日志("[系统] " + 内容, 类型);
            } catch (uiError) {
                console.warn("UI日志输出失败:", uiError.message);
            }
        }
    } catch (e) {
        console.error("安全日志记录失败:", e);
    }
}

/**
 * 初始化应用
 */
function 初始化应用() {
    try {
        console.log("=".repeat(50));
        console.log("Magic 游戏辅助脚本启动中...");
        console.log("=".repeat(50));

        应用状态.启动时间 = new Date();

        // 0. 初始化简洁日志管理器（包含控制台安全处理）
        console.log("1. 初始化简洁日志管理器...");
        if (简洁日志管理器.初始化()) {
            console.log("✅ 简洁日志管理器初始化完成");
        } else {
            console.warn("⚠️ 简洁日志管理器初始化失败");
        }

        // 1. 初始化Assets资源到存储目录
        console.log("1. 初始化Assets资源...");
        初始化Assets资源();

        // 2. 初始化配置管理
        console.log("2. 初始化配置管理...");
        if (!配置管理.初始化配置管理()) {
            throw new Error("配置管理初始化失败");
        }

        // 2. 设置主页UI布局
        console.log("2. 设置UI布局...");
        ui.layout(主页界面.布局);

        // 3. 初始化各个逻辑模块
        console.log("3. 初始化逻辑模块...");

        // 初始化主页逻辑
        if (!主页逻辑.初始化主页逻辑()) {
            throw new Error("主页逻辑初始化失败");
        }

        // 初始化日志逻辑
        if (!日志逻辑.初始化日志逻辑()) {
            throw new Error("日志逻辑初始化失败");
        }

        // 初始化抽屉页逻辑
        抽屉页逻辑.初始化抽屉逻辑("主页");

        // 4. 设置全局错误处理
        console.log("4. 设置全局错误处理...");
        设置全局错误处理();

        // 5. 检查权限状态
        console.log("5. 检查权限状态...");
        检查应用权限();

        // 6. 加载用户配置
        console.log("6. 加载用户配置...");
        var 用户配置 = 配置管理.获取所有配置();

        // 7. 根据配置决定是否自动启动脚本
        if (用户配置.应用设置 && 用户配置.应用设置.自动启动) {
            console.log("7. 自动启动脚本...");
            setTimeout(() => {
                主页逻辑.启动脚本();
            }, 2000);
        }

        // 8. 标记初始化完成
        应用状态.已初始化 = true;

        console.log("=".repeat(50));
        console.log("Magic 游戏辅助脚本启动完成！");
        console.log("启动时间:", 应用状态.启动时间.toLocaleString());
        console.log("=".repeat(50));

        // 显示欢迎消息
        安全记录日志("应用启动成功", "成功");


    } catch (e) {
        console.error("应用初始化失败:", e);
        toast("❌ 应用启动失败: " + e.message);

        // 尝试记录错误日志
        安全记录日志("应用启动失败: " + e.message, "错误");

        // 显示错误对话框
        dialogs.alert("启动失败", "应用启动失败，请检查日志信息：\n\n" + e.message);
    }
}

/**
 * 设置全局错误处理
 */
function 设置全局错误处理() {
    try {
        // AutoXjs v6.5.8 不支持 setUncaughtExceptionHandler
        // 使用 events 监听错误事件
        events.on("uncaughtException", function(e) {
            console.error("未捕获的异常:", e);

            安全记录日志("未捕获异常: " + e.message, "错误");
            toast("❌ 发生未知错误，请查看日志");
        });

        console.log("全局错误处理设置完成");
    } catch (e) {
        console.error("设置全局错误处理失败:", e);
    }
}

/**
 * 检查应用权限（优化版本，避免触发权限请求）
 */
function 检查应用权限() {
    try {
        var 缺失权限 = [];

        // 检查悬浮窗权限（安全检查）
        try {
            if (!floaty.checkPermission()) {
                缺失权限.push("悬浮窗显示");
            }
        } catch (e) {
            console.warn("检查悬浮窗权限失败:", e);
            缺失权限.push("悬浮窗显示");
        }

        // 无障碍权限检查已移除，避免应用启动时触发权限检查
        // 用户可以在菜单中手动管理无障碍权限

        // 截图权限已完全禁用检查，避免崩溃
        // 不再检查截图权限，避免触发权限请求导致应用崩溃

        if (缺失权限.length > 0) {
            console.warn("缺失权限:", 缺失权限.join(", "));
            安全记录日志("缺失权限: " + 缺失权限.join(", "), "警告");

            // 延迟提示用户授权
            setTimeout(() => {
                toast("⚠️ 请在菜单中授权必要权限");
            }, 3000);
        } else {
            console.log("基础权限检查完成");
            安全记录日志("基础权限检查完成", "成功");
        }
    } catch (e) {
        console.error("检查应用权限失败:", e);
    }
}

/**
 * 应用退出处理
 */
function 应用退出处理() {
    try {
        console.log("应用正在退出...");

        // 停止脚本
        try {
            主页逻辑.停止脚本();
        } catch (stopError) {
            console.error("停止脚本失败:", stopError);
        }

        // 清理悬浮球系统
        if (global.悬浮球管理系统) {
            try {
                global.悬浮球管理系统.销毁();
                global.悬浮球管理系统 = null;
                console.log("悬浮球系统已清理");
            } catch (e) {
                console.warn("清理悬浮球系统失败:", e);
            }
        }

        // 清理图标管理器
        try {
            var 通用图标管理器 = require("./ui/全局样式/通用图标管理器.js");
            if (通用图标管理器 && 通用图标管理器.清理资源) {
                通用图标管理器.清理资源();
                console.log("图标管理器已清理");
            }
        } catch (e) {
            console.warn("清理图标管理器失败:", e);
        }

        // 清理全局变量
        if (global.悬浮球点击处理器) {
            delete global.悬浮球点击处理器;
        }
        if (global.所有悬浮球实例) {
            global.所有悬浮球实例.length = 0;
        }

        // 保存配置
        // 配置会自动保存，无需手动操作

        // 只记录到控制台，不使用UI日志功能
        console.log("[信息] 应用正常退出");

        console.log("应用退出完成");
    } catch (e) {
        console.error("应用退出处理失败:", e);
    }
}

/**
 * 初始化Assets资源到存储目录
 */
function 初始化Assets资源() {
    try {
        console.log("开始初始化Assets资源到存储目录...");

        // 读取配置文件
        var 配置文件路径 = "app.config.json";
        if (!files.exists(配置文件路径)) {
            console.warn("配置文件不存在，跳过Assets资源初始化");
            return false;
        }

        var 配置 = JSON.parse(files.read(配置文件路径));
        var assets配置 = 配置.build.assets;

        // 目标路径：/storage/emulated/0/magic/assets/
        var 目标根路径 = assets配置.target;
        console.log("Assets目标路径:", 目标根路径);

        // 检查目标目录是否已存在
        if (files.exists(目标根路径)) {
            console.log("Assets目录已存在，跳过复制");
            return true;
        }

        // 检查存储权限
        if (!检查存储权限()) {
            console.error("缺少存储权限，无法创建Assets目录");
            return false;
        }

        // 创建目标目录
        console.log("创建Assets目录:", 目标根路径);
        files.ensureDir(目标根路径);

        // 复制Google资源文件
        复制Google资源(目标根路径);

        // 复制字体资源文件
        复制字体资源(目标根路径);

        console.log("✅ Assets资源初始化完成");
        return true;

    } catch (e) {
        console.error("❌ 初始化Assets资源失败:", e);
        return false;
    }
}

/**
 * 复制Google相关资源
 */
function 复制Google资源(目标根路径) {
    try {
        var Google目标路径 = files.join(目标根路径, "google");
        files.ensureDir(Google目标路径);

        // Google资源文件列表
        var Google资源列表 = [
            "google邮箱.png",
            "google邮箱界面.png",
            "google密码界面.png"
        ];

        Google资源列表.forEach(function(文件名) {
            var 源文件 = "assets/google/" + 文件名;
            var 目标文件 = files.join(Google目标路径, 文件名);

            if (files.exists(源文件)) {
                files.copy(源文件, 目标文件);
                console.log("✅ 复制Google资源:", 文件名);
            } else {
                console.warn("⚠️ Google资源文件不存在:", 源文件);
            }
        });

    } catch (e) {
        console.error("复制Google资源失败:", e);
    }
}

/**
 * 复制字体资源
 */
function 复制字体资源(目标根路径) {
    try {
        var 字体目标路径 = files.join(目标根路径, "fonts");
        files.ensureDir(字体目标路径);

        // 字体资源文件列表
        var 字体资源列表 = [
            "FontAwesome.ttf"
        ];

        字体资源列表.forEach(function(文件名) {
            var 源文件 = "assets/fonts/" + 文件名;
            var 目标文件 = files.join(字体目标路径, 文件名);

            if (files.exists(源文件)) {
                files.copy(源文件, 目标文件);
                console.log("✅ 复制字体资源:", 文件名);
            } else {
                console.warn("⚠️ 字体资源文件不存在:", 源文件);
            }
        });

    } catch (e) {
        console.error("复制字体资源失败:", e);
    }
}

/**
 * 检查存储权限
 */
function 检查存储权限() {
    try {
        // 尝试在存储目录创建测试文件
        var 测试路径 = "/storage/emulated/0/magic_test.txt";
        files.write(测试路径, "test");

        if (files.exists(测试路径)) {
            files.remove(测试路径);
            return true;
        }

        return false;
    } catch (e) {
        console.error("存储权限检查失败:", e);
        return false;
    }
}



// 注册应用退出事件
events.on("exit", 应用退出处理);

// 启动应用
初始化应用();
