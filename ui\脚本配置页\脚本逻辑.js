/**
 * 脚本配置页面逻辑
 * 处理脚本配置页面的交互逻辑
 */

// 导入全局样式和通用图标管理器
var 全局样式 = require('../全局样式/公共样式.js');
var 通用图标管理器 = require('../全局样式/通用图标管理器.js');

// 导入Google登录模块
var Google登录模块 = require('../../脚本/google登陆/登陆.js');

/**
 * 初始化脚本配置逻辑
 */
function 初始化脚本逻辑() {
    try {
        console.log("开始初始化脚本配置逻辑...");

        // 初始化现代图标
        初始化现代图标();

        // 注册按钮事件
        注册按钮事件();

        // 注册开关事件
        注册开关事件();

        // 注册导航事件
        注册导航事件();

        // 注册菜单按钮事件
        注册菜单按钮事件();

        // 初始化抽屉逻辑
        初始化抽屉逻辑();

        console.log("脚本配置逻辑初始化完成");
    } catch (e) {
        console.error("初始化脚本配置逻辑失败:", e);
    }
}

/**
 * 初始化现代图标
 */
function 初始化现代图标() {
    try {
        console.log("开始初始化脚本配置页面现代图标...");

        // 应用导航栏图标
        通用图标管理器.应用导航栏图标({
            脚本图标: { 颜色: 全局样式.颜色主题.白色 },
            主页图标: { 颜色: "#CCFFFFFF" },
            日志图标: { 颜色: "#CCFFFFFF" },
            菜单图标: { 颜色: 全局样式.颜色主题.白色 }
        });

        // 应用操作按钮图标
        通用图标管理器.应用操作按钮图标({
            保存按钮: true,
            重置按钮: true
        });

        console.log("脚本配置页面现代图标初始化完成");
    } catch (e) {
        console.error("初始化脚本配置页面现代图标失败:", e);
    }
}

/**
 * 注册按钮事件
 */
function 注册按钮事件() {
    try {
        // 保存配置按钮事件
        if (ui.保存配置) {
            ui.保存配置.on("click", function() {
                console.log("点击保存配置按钮");
                try {
                    var UI脚本页面 = require('./UI脚本页面.js');
                    var 保存结果 = UI脚本页面.保存配置();

                    if (保存结果) {
                        toast("✅ 配置保存成功");
                        console.log("✅ 配置保存成功");
                    } else {
                        toast("❌ 配置保存失败");
                        console.log("❌ 配置保存失败");
                    }
                } catch (e) {
                    console.error("保存配置时发生错误:", e);
                    toast("❌ 保存配置时发生错误");
                }
            });
        }

        // 重置配置按钮事件
        if (ui.重置配置) {
            ui.重置配置.on("click", function() {
                显示气泡提示("重置配置功能待开发");
            });
        }

        console.log("脚本配置页面按钮事件注册完成");
    } catch (e) {
        console.error("注册按钮事件失败:", e);
    }
}

/**
 * 注册开关事件
 */
function 注册开关事件() {
    try {
        console.log("开始注册开关事件...");

        // 登陆play商店开关事件
        if (ui.登陆play商店开关) {
            ui.登陆play商店开关.on("check", function(checked) {
                console.log("登陆play商店开关状态:", checked);

                // 自动保存配置
                try {
                    var 配置管理 = require('../../存储数据/配置管理.js');
                    配置管理.保存配置('游戏配置.登陆play商店', checked);
                    console.log("✅ 登陆play商店配置已自动保存:", checked);
                } catch (e) {
                    console.error("❌ 登陆play商店配置保存失败:", e);
                }

                if (checked) {
                    // 开关打开 - 异步启动Google登录脚本
                    启动Google登录脚本();
                    toast("✅ 登陆play商店功能已开启并保存");
                } else {
                    // 开关关闭 - 停止Google登录脚本
                    停止Google登录脚本();
                    toast("❌ 登陆play商店功能已关闭并保存");
                }
            });
        }

        // 过游戏教程开关事件
        if (ui.过游戏教程开关) {
            ui.过游戏教程开关.on("check", function(checked) {
                console.log("过游戏教程开关状态:", checked);

                // 自动保存配置
                try {
                    var 配置管理 = require('../../存储数据/配置管理.js');
                    配置管理.保存配置('游戏配置.过游戏教程', checked);
                    console.log("✅ 过游戏教程配置已自动保存:", checked);
                } catch (e) {
                    console.error("❌ 过游戏教程配置保存失败:", e);
                }

                if (checked) {
                    toast("✅ 过游戏教程功能已开启并保存");
                } else {
                    toast("❌ 过游戏教程功能已关闭并保存");
                }
            });
        }

        // 首次帐号注册开关事件
        if (ui.首次帐号注册开关) {
            ui.首次帐号注册开关.on("check", function(checked) {
                console.log("首次帐号注册开关状态:", checked);

                // 自动保存配置
                try {
                    var 配置管理 = require('../../存储数据/配置管理.js');
                    配置管理.保存配置('游戏配置.首次帐号注册', checked);
                    console.log("✅ 首次帐号注册配置已自动保存:", checked);
                } catch (e) {
                    console.error("❌ 首次帐号注册配置保存失败:", e);
                }

                if (checked) {
                    toast("✅ 首次帐号注册功能已开启并保存");
                } else {
                    toast("❌ 首次帐号注册功能已关闭并保存");
                }
            });
        }



        console.log("开关事件注册完成");
    } catch (e) {
        console.error("注册开关事件失败:", e);
    }
}

/**
 * 测试Google登录日志输出
 */
function 测试Google登录日志() {
    try {
        console.log("开始测试Google登录日志输出...");

        // 方式2：尝试直接使用UI日志模块
        try {
            console.log("尝试直接使用UI日志模块...");
            var 日志逻辑 = require('../日志页/日志逻辑.js');
            日志逻辑.添加日志("[测试] 🔄 开始测试UI日志模块", "信息");
            日志逻辑.添加日志("[测试] ✅ UI日志模块测试成功", "成功");
            显示气泡提示("✅ UI日志模块测试完成，请查看日志页面");
            return;
        } catch (uiError) {
            console.warn("UI日志模块测试失败:", uiError.message);
        }


        显示气泡提示("❌ 所有日志测试方式都失败，请查看控制台");

    } catch (e) {
        console.error("测试Google登录日志失败:", e);
        显示气泡提示("❌ 日志测试失败: " + e.message);
    }
}

/**
 * 启动Google登录脚本（异步）
 */
function 启动Google登录脚本() {
    try {
        console.log("准备启动Google登录脚本...");

        // 先测试日志输出
        测试Google登录日志();

        // 显示启动提示
        显示气泡提示("正在启动Google登录脚本...");

        // 使用简洁版启动Google登录脚本
        Google登录模块.启动_登录(
            function(结果, 错误信息) {
                if (结果) {
                    console.log("Google Play商店登录成功");
                    显示气泡提示("✅ Google Play商店登录成功");
                } else {
                    console.error("Google Play商店登录失败:", 错误信息);
                    显示气泡提示("❌ 登录失败: " + 错误信息);
                }

                // 脚本执行完成后，将开关重置为关闭状态
                if (ui.登陆play商店开关) {
                    ui.登陆play商店开关.setChecked(false);
                }
            }
        );

    } catch (e) {
        console.error("启动Google登录脚本失败:", e);
        显示气泡提示("❌ 启动异常: " + e.message);

        // 异常时重置开关状态
        if (ui.登陆play商店开关) {
            ui.登陆play商店开关.setChecked(false);
        }
    }
}

/**
 * 停止Google登录脚本
 */
function 停止Google登录脚本() {
    try {
        console.log("准备停止Google登录脚本...");

        // 显示停止提示
        显示气泡提示("正在停止Google登录脚本...");

        // 停止Google登录脚本
        Google登录模块.停止登录脚本(function(成功, 消息) {
            if (成功) {
                console.log("Google登录脚本停止成功:", 消息);
                显示气泡提示("✅ Google登录脚本已停止");
            } else {
                console.error("Google登录脚本停止失败:", 消息);
                显示气泡提示("❌ 停止失败: " + 消息);
            }
        });

    } catch (e) {
        console.error("停止Google登录脚本失败:", e);
        显示气泡提示("❌ 停止异常: " + e.message);
    }
}

/**
 * 注册菜单按钮事件
 */
function 注册菜单按钮事件() {
    try {
        // 菜单按钮点击事件 - 打开抽屉页
        if (ui.菜单按钮) {
            ui.菜单按钮.on("click", function() {
                console.log("点击菜单按钮，打开抽屉页");
                打开抽屉页();
            });
        }

        console.log("菜单按钮事件注册完成");
    } catch (e) {
        console.error("注册菜单按钮事件失败:", e);
    }
}

/**
 * 注册导航事件
 */
function 注册导航事件() {
    try {
        // 脚本配置标签点击事件（当前页面，无需切换）
        if (ui.脚本标签) {
            ui.脚本标签.on("click", function() {
                显示气泡提示("当前已在脚本配置页面");
            });
        }

        // 主页标签点击事件
        if (ui.主页标签) {
            ui.主页标签.on("click", function() {
                切换到页面("主页");
            });
        }

        // 日志标签点击事件
        if (ui.日志标签) {
            ui.日志标签.on("click", function() {
                切换到页面("日志");
            });
        }

        console.log("脚本配置页面导航事件注册完成");
    } catch (e) {
        console.error("注册导航事件失败:", e);
    }
}

/**
 * 切换到指定页面
 */
function 切换到页面(页面名称) {
    try {
        console.log("从脚本配置页面切换到:", 页面名称);

        // 清理当前页面的事件监听器
        清理页面事件();

        // 根据页面名称加载对应的页面内容
        switch (页面名称) {
            case "脚本配置":
                // 脚本配置页面已经是当前页面，无需切换
                console.log("当前已在脚本配置页面");
                break;
            case "主页":
                // 使用ui.layout加载主页页面
                var 主页页面 = require('../主页/UI主页页面.js');
                ui.layout(主页页面.布局);

                // 初始化主页逻辑
                var 主页逻辑 = require('../主页/主页逻辑.js');
                if (主页逻辑.初始化主页逻辑) {
                    主页逻辑.初始化主页逻辑();
                }
                break;
            case "日志":
                // 使用ui.layout加载日志页面
                var 日志页面 = require('../日志页/UI日志页面.js');
                ui.layout(日志页面.布局);

                // 初始化日志页面逻辑
                var 日志逻辑 = require('../日志页/日志逻辑.js');
                if (日志逻辑.初始化日志逻辑) {
                    日志逻辑.初始化日志逻辑();
                }
                break;
            default:
                console.warn("未知页面:", 页面名称);
                显示气泡提示("未知页面: " + 页面名称, "警告");
                return;
        }

        // 显示切换成功提示
        显示气泡提示("已切换到" + 页面名称 + "页面");

    } catch (e) {
        console.error("切换页面失败:", e);
        显示气泡提示("页面切换失败: " + e.message, "错误");
    }
}

/**
 * 清理页面事件监听器
 */
function 清理页面事件() {
    try {
        console.log("清理脚本配置页面事件监听器...");

        // 停止正在运行的Google登录脚本
        if (Google登录模块) {
            var 运行状态 = Google登录模块.获取运行状态();
            if (运行状态.正在运行) {
                console.log("检测到Google登录脚本正在运行，准备停止...");
                Google登录模块.停止登录脚本(function(成功, 消息) {
                    console.log("页面切换时停止脚本结果:", 成功, 消息);
                });
            }
        }

        console.log("脚本配置页面事件清理完成");
    } catch (e) {
        console.error("清理页面事件失败:", e);
    }
}

/**
 * 初始化抽屉逻辑
 */
function 初始化抽屉逻辑() {
    try {
        var 抽屉逻辑 = require('../菜单抽屉页/侧滑抽屉.js');
        抽屉逻辑.初始化抽屉逻辑("脚本配置");
        console.log("脚本配置页抽屉逻辑初始化完成");
    } catch (e) {
        console.error("初始化抽屉逻辑失败:", e);
    }
}

/**
 * 打开抽屉页
 */
function 打开抽屉页() {
    try {
        // 导入侧滑抽屉模块
        var 侧滑抽屉 = require('../菜单抽屉页/侧滑抽屉.js');

        // 打开侧滑抽屉
        侧滑抽屉.打开抽屉();

        console.log("抽屉页已打开");
    } catch (e) {
        console.error("打开抽屉页失败:", e);
        显示气泡提示("打开菜单失败", "错误");
    }
}

/**
 * 显示气泡提示
 */
function 显示气泡提示(消息, 类型) {
    try {
        // 使用toast显示提示
        toast(消息);
    } catch (e) {
        console.error("显示气泡提示失败:", e);
    }
}

// 导出模块功能
module.exports = {
    初始化脚本逻辑: 初始化脚本逻辑,
    切换到页面: 切换到页面,
    显示气泡提示: 显示气泡提示
};