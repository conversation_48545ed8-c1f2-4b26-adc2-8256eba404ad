/**
 * Magic 游戏辅助脚本 - 配置管理模块
 * 基于AutoXjs的storages模块实现配置数据持久化
 * 避免使用过时的API，使用最新的存储方案
 */

// 创建存储实例
var 配置存储 = storages.create("magic_game_config");
var 数据存储 = storages.create("magic_game_data");

// 默认配置定义
var 默认配置 = {
    // 游戏配置
    游戏配置: {
        登陆play商店: false,
        过游戏教程: false,
        首次帐号注册: false,
        每日领币: false
    },



    // 分数控制
    分数控制: {
        分数暂停: false,
        最低分数: 100,
        最高分数: 110
    },



    // 应用设置
    应用设置: {
        自动启动: false,
        启用通知: true,
        自动更新: true,
        调试模式: false
    }
};

/**
 * 获取配置项
 * @param {String} 键名 - 配置项键名，支持点分隔的嵌套键名
 * @param {*} 默认值 - 如果配置项不存在时返回的默认值
 * @returns {*} 配置项值
 */
function 获取配置(键名, 默认值) {
    try {
        if (!键名) {
            console.error("配置键名不能为空");
            return 默认值;
        }

        // 支持点分隔的嵌套键名，如 "游戏配置.自动启动游戏"
        var 键路径 = 键名.split('.');
        var 配置值 = 配置存储.get(键路径[0]);

        // 如果是嵌套键名，逐层获取
        if (键路径.length > 1) {
            for (var i = 1; i < 键路径.length; i++) {
                if (配置值 && typeof 配置值 === 'object') {
                    配置值 = 配置值[键路径[i]];
                } else {
                    配置值 = undefined;
                    break;
                }
            }
        }

        // 如果配置值不存在，返回默认值
        if (配置值 === undefined || 配置值 === null) {
            // 尝试从默认配置中获取
            var 默认配置值 = 获取默认配置值(键名);
            return 默认配置值 !== undefined ? 默认配置值 : 默认值;
        }

        return 配置值;
    } catch (e) {
        console.error("获取配置失败:", e);
        return 默认值;
    }
}

/**
 * 保存配置项
 * @param {String} 键名 - 配置项键名，支持点分隔的嵌套键名
 * @param {*} 值 - 要保存的配置值
 * @returns {Boolean} 是否保存成功
 */
function 保存配置(键名, 值) {
    try {
        if (!键名) {
            console.error("配置键名不能为空");
            return false;
        }

        var 键路径 = 键名.split('.');

        if (键路径.length === 1) {
            // 简单键名，直接保存
            配置存储.put(键名, 值);
        } else {
            // 嵌套键名，需要获取现有配置并更新
            var 根配置 = 配置存储.get(键路径[0]) || {};
            var 当前对象 = 根配置;

            // 创建嵌套对象路径
            for (var i = 1; i < 键路径.length - 1; i++) {
                if (!当前对象[键路径[i]] || typeof 当前对象[键路径[i]] !== 'object') {
                    当前对象[键路径[i]] = {};
                }
                当前对象 = 当前对象[键路径[i]];
            }

            // 设置最终值
            当前对象[键路径[键路径.length - 1]] = 值;

            // 保存根配置
            配置存储.put(键路径[0], 根配置);
        }

        console.log("配置已保存:", 键名, "=", 值);
        return true;
    } catch (e) {
        console.error("保存配置失败:", e);
        return false;
    }
}

/**
 * 获取所有配置
 * @returns {Object} 所有配置对象
 */
function 获取所有配置() {
    try {
        var 所有配置 = {};

        // 获取所有配置分组
        Object.keys(默认配置).forEach(function(分组名) {
            var 分组配置 = 配置存储.get(分组名);
            if (分组配置) {
                所有配置[分组名] = 分组配置;
            } else {
                所有配置[分组名] = 默认配置[分组名];
            }
        });

        return 所有配置;
    } catch (e) {
        console.error("获取所有配置失败:", e);
        return 默认配置;
    }
}

/**
 * 重置配置到默认值
 * @param {String} 分组名 - 可选，指定要重置的配置分组，不指定则重置所有
 * @returns {Boolean} 是否重置成功
 */
function 重置配置(分组名) {
    try {
        if (分组名) {
            // 重置指定分组
            if (默认配置[分组名]) {
                配置存储.put(分组名, 默认配置[分组名]);
                console.log("配置分组已重置:", 分组名);
            } else {
                console.error("未知的配置分组:", 分组名);
                return false;
            }
        } else {
            // 重置所有配置
            Object.keys(默认配置).forEach(function(分组) {
                配置存储.put(分组, 默认配置[分组]);
            });
            console.log("所有配置已重置为默认值");
        }

        return true;
    } catch (e) {
        console.error("重置配置失败:", e);
        return false;
    }
}

/**
 * 从默认配置中获取指定键名的值
 * @param {String} 键名 - 支持点分隔的嵌套键名
 * @returns {*} 默认配置值
 */
function 获取默认配置值(键名) {
    try {
        var 键路径 = 键名.split('.');
        var 配置值 = 默认配置[键路径[0]];

        // 如果是嵌套键名，逐层获取
        if (键路径.length > 1) {
            for (var i = 1; i < 键路径.length; i++) {
                if (配置值 && typeof 配置值 === 'object') {
                    配置值 = 配置值[键路径[i]];
                } else {
                    return undefined;
                }
            }
        }

        return 配置值;
    } catch (e) {
        console.error("获取默认配置值失败:", e);
        return undefined;
    }
}

/**
 * 验证配置数据的有效性
 * @param {Object} 配置对象 - 要验证的配置对象
 * @returns {Object} 验证结果 {valid: Boolean, errors: Array}
 */
function 验证配置(配置对象) {
    var 验证结果 = {
        valid: true,
        errors: []
    };

    try {
        if (!配置对象 || typeof 配置对象 !== 'object') {
            验证结果.valid = false;
            验证结果.errors.push("配置对象无效");
            return 验证结果;
        }

        // 验证游戏配置
        if (配置对象.游戏配置) {
            var 游戏配置 = 配置对象.游戏配置;
            if (typeof 游戏配置.登陆play商店 !== 'boolean') {
                验证结果.errors.push("登陆play商店配置必须是布尔值");
            }
            if (typeof 游戏配置.过游戏教程 !== 'boolean') {
                验证结果.errors.push("过游戏教程配置必须是布尔值");
            }
            if (typeof 游戏配置.首次帐号注册 !== 'boolean') {
                验证结果.errors.push("首次帐号注册配置必须是布尔值");
            }
            if (typeof 游戏配置.每日领币 !== 'boolean') {
                验证结果.errors.push("每日领币配置必须是布尔值");
            }
        }

        // 验证分数控制
        if (配置对象.分数控制) {
            var 分数控制 = 配置对象.分数控制;
            if (typeof 分数控制.最低分数 === 'number' && typeof 分数控制.最高分数 === 'number') {
                if (分数控制.最低分数 >= 分数控制.最高分数) {
                    验证结果.errors.push("最低分数必须小于最高分数");
                }
            }
        }

        // 如果有错误，标记为无效
        if (验证结果.errors.length > 0) {
            验证结果.valid = false;
        }

    } catch (e) {
        console.error("验证配置失败:", e);
        验证结果.valid = false;
        验证结果.errors.push("验证过程出错: " + e.message);
    }

    return 验证结果;
}

/**
 * 保存游戏数据
 * @param {String} 键名 - 数据键名
 * @param {*} 数据 - 要保存的数据
 * @returns {Boolean} 是否保存成功
 */
function 保存游戏数据(键名, 数据) {
    try {
        数据存储.put(键名, 数据);
        console.log("游戏数据已保存:", 键名);
        return true;
    } catch (e) {
        console.error("保存游戏数据失败:", e);
        return false;
    }
}

/**
 * 获取游戏数据
 * @param {String} 键名 - 数据键名
 * @param {*} 默认值 - 默认值
 * @returns {*} 游戏数据
 */
function 获取游戏数据(键名, 默认值) {
    try {
        var 数据 = 数据存储.get(键名);
        return 数据 !== undefined ? 数据 : 默认值;
    } catch (e) {
        console.error("获取游戏数据失败:", e);
        return 默认值;
    }
}

/**
 * 清空游戏数据
 * @returns {Boolean} 是否清空成功
 */
function 清空游戏数据() {
    try {
        数据存储.clear();
        console.log("游戏数据已清空");
        return true;
    } catch (e) {
        console.error("清空游戏数据失败:", e);
        return false;
    }
}

/**
 * 导出配置到JSON字符串
 * @returns {String} JSON格式的配置字符串
 */
function 导出配置() {
    try {
        var 所有配置 = 获取所有配置();
        return JSON.stringify(所有配置, null, 2);
    } catch (e) {
        console.error("导出配置失败:", e);
        return null;
    }
}

/**
 * 从JSON字符串导入配置
 * @param {String} 配置JSON - JSON格式的配置字符串
 * @returns {Boolean} 是否导入成功
 */
function 导入配置(配置JSON) {
    try {
        var 配置对象 = JSON.parse(配置JSON);
        var 验证结果 = 验证配置(配置对象);

        if (!验证结果.valid) {
            console.error("配置验证失败:", 验证结果.errors);
            return false;
        }

        // 保存各个配置分组
        Object.keys(配置对象).forEach(function(分组名) {
            if (默认配置[分组名]) {
                配置存储.put(分组名, 配置对象[分组名]);
            }
        });

        console.log("配置导入成功");
        return true;
    } catch (e) {
        console.error("导入配置失败:", e);
        return false;
    }
}

/**
 * 初始化配置管理模块
 * 确保所有默认配置都已设置
 */
function 初始化配置管理() {
    try {
        console.log("初始化配置管理模块...");

        // 检查并设置默认配置
        Object.keys(默认配置).forEach(function(分组名) {
            var 现有配置 = 配置存储.get(分组名);
            if (!现有配置) {
                配置存储.put(分组名, 默认配置[分组名]);
                console.log("已设置默认配置:", 分组名);
            }
        });

        console.log("配置管理模块初始化完成");
        return true;
    } catch (e) {
        console.error("初始化配置管理失败:", e);
        return false;
    }
}

// 导出模块功能
module.exports = {
    // 配置管理
    获取配置: 获取配置,
    保存配置: 保存配置,
    获取所有配置: 获取所有配置,
    重置配置: 重置配置,
    验证配置: 验证配置,

    // 游戏数据管理
    保存游戏数据: 保存游戏数据,
    获取游戏数据: 获取游戏数据,
    清空游戏数据: 清空游戏数据,

    // 导入导出
    导出配置: 导出配置,
    导入配置: 导入配置,

    // 初始化
    初始化配置管理: 初始化配置管理,

    // 默认配置（只读）
    默认配置: 默认配置
};